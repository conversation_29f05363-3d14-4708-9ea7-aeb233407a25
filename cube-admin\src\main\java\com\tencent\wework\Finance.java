package com.tencent.wework;

public class Finance {
	public native static long NewSdk();

	/**
	 * ?????
	 * Return?=0???API????
	 *
	 * @param [in]  sdk			NewSdk???sdk??
	 * @param [in]  corpid      ???????id????wwd08c8exxxx5ab44d???????????--????--??????
	 * @param [in]  secret		???????Secret???????????--????--????????
	 *
	 * @return ?????????
	 *      0   - ??
	 *      !=0 - ??
	 */
	public native static int Init(long sdk, String corpid, String secret);

	/**
	 * ????????
	 * Return?=0???API????
	 *
	 *
	 * @param [in]  sdk				NewSdk???sdk??
	 * @param [in]  seq				????seq?????????????????seq+1?????seq??????????seq?????????seq:0
	 * @param [in]  limit			?????????????1000????1000??????
	 * @param [in]  proxy			????????????????????socks5://10.0.0.1:8081 ?? http://10.0.0.1:8081
	 * @param [in]  passwd			???????????????????? user_name:passwd_123
	 * @param [out] chatDatas		????????????slice???.????errcode/errmsg??????????
	 *
	 * @return ????????
	 *      0   - ??
	 *      !=0 - ??
	 */
	public native static int GetChatData(long sdk, long seq, long limit, String proxy, String passwd, long timeout, long chatData);

	/**
	 * ????????
	 * Return?=0???API????
	 *
	 *
	 * @param [in]  sdk				NewSdk???sdk??
	 * @param [in]  sdkFileid		?GetChatData????????????????sdkfileid
	 * @param [in]  proxy			????????????????????socks5://10.0.0.1:8081 ?? http://10.0.0.1:8081
	 * @param [in]  passwd			???????????????????? user_name:passwd_123
	 * @param [in]  indexbuf		???????????????????????????????????512k??????????????????outindexbuf?????
	 * @param [out] media_data		???????????.MediaData???.????data(????)/outindexbuf(????)/is_finish(??????)

	 *
	 * @return ????????
	 *      0   - ??
	 *      !=0 - ??
	 */
	public native static int GetMediaData(long sdk, String indexbuf, String sdkField, String proxy, String passwd, long timeout, long mediaData);

	/**
	 * @brief ????
	 * @param [in]  encrypt_key, getchatdata???encrypt_key
	 * @param [in]  encrypt_msg, getchatdata???content
	 * @param [out] msg, ???????
	 * @return ????????
	 *      0   - ??
	 *      !=0 - ??
	 */
	public native static int DecryptData(long sdk, String encrypt_key, String encrypt_msg, long msg);

	public native static void DestroySdk(long sdk);
	public native static long NewSlice();
	/**
	 * @brief ??slice??NewSlice????
	 * @return
	 */
	public native static void FreeSlice(long slice);

	/**
	 * @brief ??slice??
	 * @return ??
	 */
	public native static String GetContentFromSlice(long slice);

	/**
	 * @brief ??slice????
	 * @return ??
	 */
	public native static int GetSliceLen(long slice);
	public native static long NewMediaData();
	public native static void FreeMediaData(long mediaData);

	/**
	 * @brief ??mediadata outindex
	 * @return outindex
	 */
	public native static String GetOutIndexBuf(long mediaData);
	/**
	 * @brief ??mediadata data??
	 * @return data
	 */
	public native static byte[] GetData(long mediaData);
	public native static int GetIndexLen(long mediaData);
	public native static int GetDataLen(long mediaData);

	/**
	 * @brief ??mediadata????
	 * @return 1???0???
	 */
	public native static int IsMediaDataFinish(long mediaData);

	static {
		System.setProperty("java.library.path", "/usr/lib");
		System.loadLibrary("WeWorkFinanceSdk");
	}
}



