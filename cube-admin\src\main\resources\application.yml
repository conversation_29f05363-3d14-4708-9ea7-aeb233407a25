# 项目相关配置
ruoyi:
  # 名称
  name: ucube
  # 版本
  version: 1.0
  # 版权年份
  copyrightYear: 2024
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/AGI/chatfile
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8081
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.anal: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: 127.0.0.1
    port: 26379
    # 数据库索引
    database: 0
    # 密码
    password: qwe#123
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30000000

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.cube.**.domain,com.cube.**.entity
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql
  reasonable: false
  aggregateFunctions: count,avg,max,min,sum

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping:

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*


# 此处替换为你自己的企业微信参数
wechat:
  corpId:   #企业微信企业ID，在我的企业查看
  agentSecret:   #小程序应用秘钥，在应用中查看
  chatSecret:
  pcAgentSecret:   #PC应用秘钥，在应用中查看

# 此处替换为你自己的服务号秘钥
office:
  appId:
  appSecret:
upload:
#上传文件路径，和上面的profile保持一致。例如：file:D:/AGI/chatfile/
  url: /chatfile/

#数据智能专区SDK
datazone:
  url: https://qyapi.weixin.qq.com/cgi-bin/chatdata/sync_call_program?access_token=

#以太坊账户密钥存储地址
geth:
  mainAddress:
  mainPrivateKey:
  httpUrl:

TencentDoc:
  url: https://docs.qq.com/oauth/v2/token
  clientId:
  clientSecret:
  redirectUri: https%3A%2F%2Fdocs.qq.com%2Foauth%2Fv2%2Fmp%2Fredirect%2Fdefault

# 腾讯元气空间（元宝）配置
yuanbao:
  # 应用ID
  appId: 
  # 密钥
  secretKey: 
  # 模型配置
  models:
    # 元气空间T1模型
    hunyuan:
      modelId: yb-hunyuan-pt
      modelIdSdsk: yb-hunyuan-sdsk  # 深度思考
      modelIdLwss: yb-hunyuan-lwss  # 联网搜索
    # 元气空间DS模型
    deepseek:
      modelId: yb-deepseek-pt
      modelIdSdsk: yb-deepseek-sdsk  # 深度思考
      modelIdLwss: yb-deepseek-lwss  # 联网搜索
