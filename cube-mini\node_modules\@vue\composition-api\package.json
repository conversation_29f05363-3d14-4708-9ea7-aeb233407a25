{"name": "@vue/composition-api", "version": "1.7.2", "packageManager": "pnpm@8.6.12", "description": "Provide logic composition capabilities for Vue.", "keywords": ["vue", "composition-api"], "repository": {"type": "git", "url": "git+https://github.com/vuejs/composition-api.git"}, "main": "./index.js", "module": "./dist/vue-composition-api.mjs", "unpkg": "./dist/vue-composition-api.prod.js", "jsdelivr": "./dist/vue-composition-api.prod.js", "types": "./dist/vue-composition-api.d.ts", "exports": {".": {"import": "./dist/vue-composition-api.mjs", "types": "./dist/vue-composition-api.d.ts", "require": "./index.js"}, "./*": "./*"}, "author": {"name": "liximomo", "email": "<EMAIL>"}, "license": "MIT", "sideEffects": false, "files": ["dist", "index.js"], "scripts": {"start": "rollup -c -w", "build": "rimraf dist && rollup -c", "lint": "prettier --write --parser typescript \"{src,test,test-dts}/**/*.ts?(x)\" && prettier --write \"{src,test}/**/*.js\"", "test": "vitest", "test:all": "pnpm run test && pnpm run test:dts", "test:dts": "tsc -p ./test-dts/tsconfig.json && tsc -p ./test-dts/tsconfig.vue3.json && pnpm run build && tsc -p ./test-dts/tsconfig.build.json", "update-readme": "node ./scripts/update-readme.js", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s && pnpm run update-readme && git add CHANGELOG.md README.*", "release": "bumpp -x \"npm run changelog\" --all && npm publish", "release-gh": "conventional-github-releaser -p angular", "prepublishOnly": "npm run build"}, "bugs": {"url": "https://github.com/vuejs/composition-api/issues"}, "homepage": "https://github.com/vuejs/composition-api#readme", "peerDependencies": {"vue": ">= 2.5 < 2.7"}, "devDependencies": {"@rollup/plugin-node-resolve": "^13.3.0", "@rollup/plugin-replace": "^4.0.0", "@types/node": "^17.0.31", "bumpp": "^9.1.1", "conventional-changelog-cli": "^2.2.2", "conventional-github-releaser": "^3.1.5", "jsdom": "^20.0.0", "lint-staged": "^14.0.0", "prettier": "^3.0.1", "rimraf": "^5.0.1", "rollup": "^2.72.0", "rollup-plugin-dts": "^4.2.1", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.2", "simple-git-hooks": "^2.9.0", "tslib": "^2.4.0", "typescript": "^4.6.4", "vitest": "^0.34.1", "vue": "^2.6.14", "vue3": "npm:vue@3.2.21", "vue-router": "^3.5.3", "vue-server-renderer": "^2.6.14"}, "simple-git-hooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.js": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "prettier": {"semi": false, "singleQuote": true, "printWidth": 80}}