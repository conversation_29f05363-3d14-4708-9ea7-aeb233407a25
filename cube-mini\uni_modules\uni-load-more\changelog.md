## 1.3.3（2022-01-20）
- 新增 showText属性 ，是否显示文本
## 1.3.2（2022-01-19）
- 修复 nvue 平台下不显示文本的bug
## 1.3.1（2022-01-19）
- 修复 微信小程序平台样式选择器报警告的问题
## 1.3.0（2021-11-19）
- 优化 组件UI，并提供设计资源，详见:[https://uniapp.dcloud.io/component/uniui/resource](https://uniapp.dcloud.io/component/uniui/resource)
- 文档迁移，详见:[https://uniapp.dcloud.io/component/uniui/uni-load-more](https://uniapp.dcloud.io/component/uniui/uni-load-more)
## 1.2.1（2021-08-24）
- 新增 支持国际化
## 1.2.0（2021-07-30）
- 组件兼容 vue3，如何创建vue3项目，详见 [uni-app 项目支持 vue3 介绍](https://ask.dcloud.net.cn/article/37834)
## 1.1.8（2021-05-12）
- 新增 组件示例地址
## 1.1.7（2021-03-30）
- 修复 uni-load-more 在首页使用时，h5 平台报 'uni is not defined' 的 bug
## 1.1.6（2021-02-05）
- 调整为uni_modules目录规范
