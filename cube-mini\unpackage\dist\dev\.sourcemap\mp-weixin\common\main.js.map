{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/JavaWorkSpace/U3W-AI/cube-mini/App.vue?c999", "uni-app:///App.vue", "webpack:///D:/JavaWorkSpace/U3W-AI/cube-mini/App.vue?6cb8", "webpack:///D:/JavaWorkSpace/U3W-AI/cube-mini/App.vue?978f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "use", "plugins", "config", "productionTip", "prototype", "$store", "store", "App", "mpType", "app", "$mount", "onLaunch", "methods", "initData", "uni", "success", "statusBarHeight", "menuWidth", "menuHeight", "menuBorderRadius", "menuRight", "menuTop", "contentTop", "fail", "console", "initApp", "initConfig", "checkLogin"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAAgD;AAC3G;AACA;AACA;AACA;AAAqB;AAAA;AALrB;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAIpC;AACtBC,YAAG,CAACC,GAAG,CAACC,gBAAO,CAAC;AAEhBF,YAAG,CAACG,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCJ,YAAG,CAACK,SAAS,CAACC,MAAM,GAAGC,cAAK;AAE5BC,YAAG,CAACC,MAAM,GAAG,KAAK;AAElB,IAAMC,GAAG,GAAG,IAAIV,YAAG,mBACdQ,YAAG,EACN;AAEF,UAAAE,GAAG,EAACC,MAAM,EAAE,C;;;;;;;;;;;;;AClBZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AACyK;AACzK,gBAAgB,sLAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAA8nB,CAAgB,0pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACClpB;AACA;AACA;AAEA,eAEA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MACAC;QACAC;UACA;UACA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UAEA;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;;UACAR;QACA;QACAS;UACAC;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IAIA;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvEA;AAAA;AAAA;AAAA;AAAisC,CAAgB,6qCAAG,EAAC,C;;;;;;;;;;;ACArtC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';import Vue from 'vue'\r\nimport App from './App'\r\nimport store from './store' // store\r\nimport plugins from './plugins' // plugins\r\nimport './permission' // permission\r\nVue.use(plugins)\r\n\r\nVue.config.productionTip = false\r\nVue.prototype.$store = store\r\n\r\nApp.mpType = 'app'\r\n\r\nconst app = new Vue({\r\n  ...App\r\n})\r\n\r\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n\timport config from './config'\r\n\timport store from '@/store'\r\n\timport {\r\n\t\tgetToken\r\n\t} from '@/utils/auth'\r\n\r\n\texport default {\r\n\t\tonLaunch: function() {\r\n\t\t\tthis.initApp();\r\n\t\t\tthis.initData();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinitData() {\r\n\t\t\t\tuni.getSystemInfo({\r\n\t\t\t\t\tsuccess: (result) => {\r\n\t\t\t\t\t\t// 获取手机系统的状态栏高度（不同手机的状态栏高度不同）  （ 不要使用uni-app官方文档的var(--status-bar-height) 官方这个是固定的20px  不对的 ）\r\n\t\t\t\t\t\t// console.log('当前手机的状态栏高度',result.statusBarHeight)\r\n\t\t\t\t\t\tlet statusBarHeight = result.statusBarHeight + 'px'\r\n\r\n\t\t\t\t\t\t// 获取右侧胶囊的信息 单位px\r\n\t\t\t\t\t\tconst menuButtonInfo = uni.getMenuButtonBoundingClientRect()\r\n\t\t\t\t\t\t//bottom: 胶囊底部距离屏幕顶部的距离\r\n\t\t\t\t\t\t//height: 胶囊高度\r\n\t\t\t\t\t\t//left:   胶囊左侧距离屏幕左侧的距离\r\n\t\t\t\t\t\t//right:  胶囊右侧距离屏幕左侧的距离\r\n\t\t\t\t\t\t//top:    胶囊顶部距离屏幕顶部的距离\r\n\t\t\t\t\t\t//width:  胶囊宽度\r\n\t\t\t\t\t\t// console.log(menuButtonInfo.width, menuButtonInfo.height, menuButtonInfo.top)\r\n\t\t\t\t\t\t// console.log('计算胶囊右侧距离屏幕右边距离', result.screenWidth - menuButtonInfo.right)\r\n\t\t\t\t\t\tlet menuWidth = menuButtonInfo.width + 'px'\r\n\t\t\t\t\t\tlet menuHeight = menuButtonInfo.height + 'px'\r\n\t\t\t\t\t\tlet menuBorderRadius = menuButtonInfo.height / 2 + 'px'\r\n\t\t\t\t\t\tlet menuRight = result.screenWidth - menuButtonInfo.right + 'px'\r\n\t\t\t\t\t\tlet menuTop = menuButtonInfo.top + 'px'\r\n\t\t\t\t\t\tlet contentTop = result.statusBarHeight + 44 + 'px'\r\n\r\n\t\t\t\t\t\tlet menuInfo = {\r\n\t\t\t\t\t\t\tstatusBarHeight: statusBarHeight, //状态栏高度----用来给自定义导航条页面的顶部导航条设计padding-top使用：目的留出系统的状态栏区域\r\n\t\t\t\t\t\t\tmenuWidth: menuWidth, //右侧的胶囊宽度--用来给自定义导航条页面的左侧胶囊设置使用\r\n\t\t\t\t\t\t\tmenuHeight: menuHeight, //右侧的胶囊高度--用来给自定义导航条页面的左侧胶囊设置使用\r\n\t\t\t\t\t\t\tmenuBorderRadius: menuBorderRadius, //一半的圆角--用来给自定义导航条页面的左侧胶囊设置使用\r\n\t\t\t\t\t\t\tmenuRight: menuRight, //右侧的胶囊距离右侧屏幕距离--用来给自定义导航条页面的左侧胶囊设置使用\r\n\t\t\t\t\t\t\tmenuTop: menuTop, //右侧的胶囊顶部距离屏幕顶部的距离--用来给自定义导航条页面的左侧胶囊设置使用\r\n\t\t\t\t\t\t\tcontentTop: contentTop, //内容区距离页面最上方的高度--用来给自定义导航条页面的内容区定位距离使用\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.setStorageSync('menuInfo', menuInfo)\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (error) => {\r\n\t\t\t\t\t\tconsole.log(error)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 初始化应用\r\n\t\t\tinitApp() {\r\n\t\t\t\t// 初始化应用配置\r\n\t\t\t\tthis.initConfig()\r\n\t\t\t\t// 检查用户登录状态\r\n\t\t\t\t//#ifdef H5\r\n\t\t\t\tthis.checkLogin()\r\n\t\t\t\t//#endif\r\n\t\t\t},\r\n\t\t\tinitConfig() {\r\n\t\t\t\tthis.globalData.config = config\r\n\t\t\t},\r\n\t\t\tcheckLogin() {\r\n\t\t\t\tif (!getToken()) {\r\n\t\t\t\t\tthis.$tab.reLaunch('/pages/index')\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t@import '@/static/scss/index.scss'\r\n</style>", "import mod from \"-!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751814916176\n      var cssReload = require(\"D:/hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}