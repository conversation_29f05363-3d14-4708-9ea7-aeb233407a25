{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pagesA/aiChat/aiChat.vue?a195", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pagesA/aiChat/aiChat.vue?add9", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pagesA/aiChat/aiChat.vue?aa5f", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pagesA/aiChat/aiChat.vue?bb9c", "uni-app:///pagesA/aiChat/aiChat.vue", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pagesA/aiChat/aiChat.vue?7d91", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pagesA/aiChat/aiChat.vue?c95b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "uaMarkdown", "data", "shareSession", "userName", "user", "assistant", "isNonFlow", "sandMsgButFlag", "scrollTop", "scrollViewHeight", "html", "fileUp", "show", "tempFileUrl", "type", "checkboxFlag", "CheckboxTempData", "chooseIndex", "editWindowValue", "chatsList", "userInput", "conversationId", "fileUrl", "chatType", "userList", "winSize", "showShade", "showPop", "popButton", "index", "name", "icon", "color", "isLine", "sessionSearchValue", "popStyle", "pickerUserIndex", "animation", "characterInput", "InputBottom", "sessionList", "modalName", "statusBarHeight", "menuWidth", "menuHeight", "menuBorderRadius", "menuRight", "menuTop", "contentTop", "mounted", "onLoad", "onShow", "methods", "getUserName", "markdownToPlainText", "replace", "ShareSessionPoster", "renderGeneratedPicture", "fileType", "pathType", "quality", "success", "path", "fail", "copySessionContent", "uni", "title", "console", "scrollToBottom", "monitorSessionSearchValue", "sessionSearch", "res", "item", "_this", "toggleCheckbox", "delMultipleSessions", "content", "tempArr", "duration", "delTempFile", "buttonFunction", "editWindowConfirm", "delWindowConfirm", "goOneSession", "newSession", "sandMsg", "getUserChatHistory", "getChatHistoryDetail", "chatV2Completions", "params", "userPrompt", "endIndex", "lastElement", "tempObj", "text", "chatCompletions", "url", "timeout", "responseType", "method", "enableChunked", "requestTask", "jsonArray", "contentText", "onLongPress", "touches", "style", "setTimeout", "hidePop", "openFileUpPopup", "photograph", "count", "sizeType", "sourceType", "chooseImage", "openFile", "closeFileUpPopup", "uploadFiles", "filePath", "streamRecord", "manager", "lang", "endStreamRecord", "initRecord", "InputFocus", "InputBlur", "showModal", "hideModal", "tabSelect", "goToBack", "delta"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACyB;;;AAG9E;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,6VAEN;AACP,KAAK;AACL;AACA,aAAa,6VAEN;AACP,KAAK;AACL;AACA,aAAa,mWAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,mWAEN;AACP,KAAK;AACL;AACA,aAAa,gWAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjFA;AAAA;AAAA;AAAA;AAAo0B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACsUx1B;AASA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;EACA;AACA;AAEA;AACA;AACA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;MACA;MACA;MACA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACA;MACAC;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;QACAC;MAEA,GACA;QACAH;QACAC;QACAC;QACAE;MACA,EAEA;MACAC;MACA;MACAC;MACA;MACAC;MAGAC;MACAC;MACAC;MACAC;QACAV;MACA,GACA;QACAA;MACA,GACA;QACAA;MACA,GACA;QACAA;MACA,GACA;QACAA;MACA,GACA;QACAA;MACA,EACA;MACAW;MAEAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;IAEA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA,yBACAC;MAAA,CACAA;MAAA,CACAA;MAAA,CACAA;MAAA,CACAA;MAAA,CACAA;MAAA,CACAA;MAAA,CACAA;MAAA,CACAA;MAAA,CACAA;;MAEA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAC;QACA;QACAC;QACAC;QACAC;UACA;UACAnE;YACAoE;UACA;QACA;QACAC;UACA;QAAA;MAEA;IACA;IACA;IACAC;MACA;MACAC;QACAhE;QACA4D;UACAI;YACAC;YACAnC;UACA;QACA;QACAgC;UACAI;UACAF;YACAC;YACAnC;UACA;QACA;MACA;IACA;IACA;IACAqC;MAAA;MACA;QACAH;UACA;UACA;UACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAI;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAJ;MACA;QACA;QACAK;UACAC;QACA;QACAC;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAV;UACAW;UACAf;YACA;cACA;cACAY;gBACAI;cACA;cACA;gBACA;gBACA;kBACAJ;gBACA;gBACAA;gBACAA;cACA;YACA;cACA;YAAA;UAEA;QACA;MAEA;QACAR;UACAlC;UACAmC;UACAY;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;QACA;MACA;QAEA;MAEA;QAEA;MAEA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;MACA;QACA;QACA;UACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA,uBACA,kDACA;QACA;QACA;UACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;QACA;UACA;UACA;YACA;cACA;cACA;gBACA;gBACA;cACA,GACA;gBACA;kBACA;kBACA;gBACA;gBACA;cACA;YAEA;YAEA;YAEA;YAEA;YACA;YACA;YAEA;UAEA;YACA;cACA;cACA;gBACA;gBACA;cACA;YACA;YAEA;YAEA;YAEA;YAEA;YACA;UACA;QACA;UACA;YACA;YACA;cACA;cACA;YACA;UACA;UAEA;UAEA;UAEA;UAEA;UACA;QACA;MAEA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACApB;gBACA;cAAA;gBAFAK;gBAGA;gBACA;kBACAA;oBACAC;kBACA;kBACA;kBACA;;kBAGA;oBACA;kBACA;oBACA;oBACA;oBACA;kBACA;;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAe;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;kBACAlE;gBACA;kBACA;kBACA;oBACA;sBACA;wBACA;sBACA;wBACA;wBACA;wBACA;sBACA;oBACA;sBACA;oBACA;kBAEA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAmE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAf;gBAEAgB;kBACAC;kBACArE;gBACA;gBAEAsE;gBACAC;gBAEA;kBACAC;oBACA;oBACA;sBACA;sBACA;oBACA;kBACA;kBACApB;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAF;gBACA;gBACA;kBACAE,uDACAqB;kBACArB;kBACAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IACA;IACAsB;MACA;MAEA;QACAL;QACArE;QACAC;QACA;QACAC;MACA;MAEA;QACAyE,2DACAP;QAAA;QACAQ;QACAC;QACAC;QACAC;QAAA;QACAvC;QACAE;MACA;MACAsC;MACA;MACAA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;QAEA;UACA;YACA;YACA;cACA;cACA;YACA;UACA;UACA5B;QACA;;QAEA;QACA;;QAEA;QACA6B;UACA;UACA;YACA;YACA;YACA;cACA;gBACA7B;cACA;cACA;gBACA;kBACA;kBACA;kBACA;oBACA8B;oBACA9B,+DACA8B;oBACA9B;kBACA;oBACA8B;oBACA;sBACA9B,uDACAqB;sBACArB;oBACA;kBACA;gBACA;cACA;YACA;cACAN;YACA;UACA;QACA;MACA;IACA;IACA,UACAqC;MAAA;MACA;QAAAC;QAAAC;QAAA7E;;MAEA;MACA;QACA6E;MACA;QACAA;MACA;MACA;QACAA;MACA;QACAA;MACA;MAEA;MACA;MACA;MACA;MACA;QACAC;UACA;QACA;MACA;IACA;IACA,UACAC;MAAA;MACA;MACA;MACAD;QACA;MACA;IACA;IACA;IACAE;MACA;IACA;IACA;IACAC;MACA;MAEA;MACA7C;QACA8C;QACAC;QAAA;QACAC;QAAA;QACApD;UACAY;QACA;QACAV;UACAI;QACA;MACA;IACA;IACA;IACA+C;MACA;MAEA;MACAjD;QACA8C;QACAC;QAAA;QACAC;QAAA;QACApD;UACA;UACAY;QACA;QACAV;UACAI;QACA;MACA;IACA;IACA;IACAgD;MACA;MAEA;MACAzH;QACAqH;QACAjG;QACA+C;UAAA;YAAA;cAAA;gBAAA;kBAAA;oBACA;oBACAY;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CACA;UAAA;YAAA;UAAA;UAAA;QAAA;QACAV;UACA;UACA;QACA;MACA;IACA;IACA;IACAqD;MACA;IACA;IACA;IACAC;MACA;MACA;MACApD;QACA+B;QACAsB;QACAxF;QACA+B;UACA;UACA;UACA;YACAY;YACAA;YACAA;YAEA;cACAA;YACA;UAEA;QACA;MACA;IACA;IACA;IACA8C;MACA;MACA;MACAC;QACAC;MACA;IACA;IACAC;MACA;MACA;MACAF;IACA;IACAG;MAAA;MACA;MACAH;QACA;QACA;MACA;MACA;MACAA;QACA;QACA;QACA;UACA;QACA;QACA;QACA;QACA;MAEA;IACA;IAEAI;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACAhE;QACAiE;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpkCA;AAAA;AAAA;AAAA;AAA8gD,CAAgB,+4CAAG,EAAC,C;;;;;;;;;;;ACAliD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesA/aiChat/aiChat.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pagesA/aiChat/aiChat.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./aiChat.vue?vue&type=template&id=0373616b&\"\nvar renderjs\nimport script from \"./aiChat.vue?vue&type=script&lang=js&\"\nexport * from \"./aiChat.vue?vue&type=script&lang=js&\"\nimport style0 from \"./aiChat.vue?vue&type=style&index=0&lang=less&scope=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesA/aiChat/aiChat.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./aiChat.vue?vue&type=template&id=0373616b&\"", "var components\ntry {\n  components = {\n    lPainter: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/lime-painter/components/l-painter/l-painter\" */ \"@/uni_modules/lime-painter/components/l-painter/l-painter.vue\"\n      )\n    },\n    lPainterView: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/lime-painter/components/l-painter-view/l-painter-view\" */ \"@/uni_modules/lime-painter/components/l-painter-view/l-painter-view.vue\"\n      )\n    },\n    lPainterText: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/lime-painter/components/l-painter-text/l-painter-text\" */ \"@/uni_modules/lime-painter/components/l-painter-text/l-painter-text.vue\"\n      )\n    },\n    lPainterImage: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/lime-painter/components/l-painter-image/l-painter-image\" */ \"@/uni_modules/lime-painter/components/l-painter-image/l-painter-image.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniBadge: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-badge/components/uni-badge/uni-badge\" */ \"@/uni_modules/uni-badge/components/uni-badge/uni-badge.vue\"\n      )\n    },\n    uniSearchBar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar\" */ \"@/uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog\" */ \"@/uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.characterInput = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.characterInput = true\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./aiChat.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./aiChat.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"bg-gradual-blue\">\n\t\t<!--  全屏主体页面 -->\n\t\t<view class=\"DrawerPage\" :class=\"modalName=='viewModal'?'show':''\">\n\n\t\t\t<!-- 海报 -->\n\t\t\t<view style=\"z-index: 999; position:absolute; width: 400px; \">\n\t\t\t\t<l-painter hidden=\"true\" ref=\"painter\">\n\t\t\t\t\t<l-painter-view\n\t\t\t\t\t\tcss=\" background: #F1F1F1; justify-content: content; color: #FFFFFF; padding: 100rpx 40rpx;\">\n\n\t\t\t\t\t\t<!-- 用户 -->\n\t\t\t\t\t\t<l-painter-view css=\" display: flex; justify-content: flex-end; \">\n\t\t\t\t\t\t\t<l-painter-view\n\t\t\t\t\t\t\t\tcss=\"padding: 20rpx; border-radius: 15rpx; background: #1EC57C; max-width: 100%; \">\n\t\t\t\t\t\t\t\t<l-painter-text :text=\"shareSession.user\" />\n\t\t\t\t\t\t\t</l-painter-view>\n\t\t\t\t\t\t</l-painter-view>\n\n\t\t\t\t\t\t<!-- 嘟嘟 -->\n\t\t\t\t\t\t<l-painter-view\n\t\t\t\t\t\t\tcss=\"margin-top: 60rpx;  display: flex; justify-content: flex-start; align-items: center; color: #2f2f2f;\">\n\t\t\t\t\t\t\t<l-painter-image src=\"/static/images/dudu.gif\"\n\t\t\t\t\t\t\t\tcss=\"width: 40rpx; height: 40rpx;margin-right: 6rpx;\" />\n\t\t\t\t\t\t\t<l-painter-text text=\"嘟嘟\" />\n\t\t\t\t\t\t</l-painter-view>\n\t\t\t\t\t\t<l-painter-view\n\t\t\t\t\t\t\tcss=\" margin-top: 10rpx; padding-bottom: 20rpx;  display: flex; justify-content: flex-start; background: #FFFFFF; color: #353535; border-radius: 15rpx; \">\n\t\t\t\t\t\t\t<l-painter-view css=\" width: 95%;  \">\n\t\t\t\t\t\t\t\t<l-painter-text :text=\"shareSession.assistant\" css=\"width: 100%; padding: 20rpx;\" />\n\t\t\t\t\t\t\t</l-painter-view>\n\t\t\t\t\t\t</l-painter-view>\n\n\n\t\t\t\t\t\t<!-- 底部 -->\n\t\t\t\t\t\t<l-painter-view css=\" display: flex; color:#000; margin-top: 80rpx; \">\n\t\t\t\t\t\t\t<l-painter-view\n\t\t\t\t\t\t\t\tcss=\" flex: 4; display: flex; justify-content: center; align-items: center; height: 150rpx;\">\n\t\t\t\t\t\t\t\t<l-painter-view>\n\t\t\t\t\t\t\t\t\t<l-painter-text :text=\"shareSession.userName+'推荐给你'\"\n\t\t\t\t\t\t\t\t\t\tcss=\"display: block; padding: 10rpx; font-size: 30rpx;\" />\n\t\t\t\t\t\t\t\t\t<l-painter-text text=\"长按或扫描识别二维码\"\n\t\t\t\t\t\t\t\t\t\tcss=\"display: block; padding: 10rpx; font-size: 22rpx; color: #8a8a8a;\" />\n\t\t\t\t\t\t\t\t</l-painter-view>\n\t\t\t\t\t\t\t</l-painter-view>\n\t\t\t\t\t\t\t<l-painter-view css=\"flex: 2; \">\n\t\t\t\t\t\t\t\t<l-painter-image src=\"/static/QRcode.jpg\"\n\t\t\t\t\t\t\t\t\tcss=\"width: 150rpx; height: 150rpx;margin-right: 100rpx;\" />\n\t\t\t\t\t\t\t</l-painter-view>\n\t\t\t\t\t\t</l-painter-view>\n\t\t\t\t\t</l-painter-view>\n\t\t\t\t</l-painter>\n\t\t\t\t<view style=\"height: ;\">\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<!-- 主屏 -->\n\t\t\t<scroll-view scroll-y>\n\t\t\t\t<view class=\"page_box\">\n\t\t\t\t\t<!-- 行内式直接变量小程序不支持，故需要写成动态的变量 -->\n\t\t\t\t\t<view class=\"my_tab\" :style=\"{height:contentTop}\">\n\t\t\t\t\t\t<view class=\"my_tab_title\" :style=\"{paddingTop:contentTop}\">\n\t\t\t\t\t\t\t<!-- 左侧自定义胶囊 -->\n\t\t\t\t\t\t\t<view class=\"menu_btn\" style=\"z-index: 10;\"\n\t\t\t\t\t\t\t\t:style=\"{ position: 'fixed',top:menuTop,left:menuRight,width:menuWidth,height:menuHeight, border: '1rpx solid #ddd',borderRadius:menuBorderRadius,}\">\n\t\t\t\t\t\t\t\t<uni-icons @click=\"goToBack\" class=\"arrowleft\" type=\"left\" :color=\"'#000'\" size=\"20\" />\n\t\t\t\t\t\t\t\t<text class=\"text_box\"></text>\n\t\t\t\t\t\t\t\t<view @click=\"showModal\" data-target=\"viewModal\">\n\t\t\t\t\t\t\t\t\t<uni-icons class=\"home\" type=\"bars\" :color=\"'#000'\" size=\"20\" />\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\tstyle=\" z-index: 1; position: fixed; font-family: `Microsoft YaHei` ;font-size: 32rpx; width: 100%;display: flex;align-items: center;   justify-content: center;\"\n\t\t\t\t\t\t\t\t:style=\"{top:menuTop,height:menuHeight,}\"><span :style=\"{height:menuHeight,}\"\n\t\t\t\t\t\t\t\t\tstyle=\"display: flex;align-items: center;   justify-content: center; \">\n\t\t\t\t\t\t\t\t\t<div>嘟<span style=\"padding-left: 2rpx;\">嘟</span></div>\n\t\t\t\t\t\t\t\t</span></span>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<!-- 内容区↓ ↓ ↓ ↓ ↓ ↓ -->\n\t\t\t\t\t<view style=\"display: block;\">\n\t\t\t\t\t\t<!-- 聊天内容框 -->\n\t\t\t\t\t\t<scroll-view class=\"scroll-view\" :style=\"{height:scrollViewHeight+'px'}\" :scroll-y=\"true\"\n\t\t\t\t\t\t\t:scroll-top=\"scrollTop\" :scroll-with-animation=\"true\">\n\t\t\t\t\t\t\t<view id=\"scroll-view-content\">\n\t\t\t\t\t\t\t\t<view class=\"cu-chat\" v-for=\"(item, index) in chatsList\" :key=\"index\">\n\t\t\t\t\t\t\t\t\t<view v-if='item.role === \"user\"' v-for=\"(ite, ind) in item.content\" :key=\"ind\">\n\t\t\t\t\t\t\t\t\t\t<!-- 图片 -->\n\t\t\t\t\t\t\t\t\t\t<view v-if=\"ite.type == 'file_url'\" class=\"cu-item self\">\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"main\">\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"ite.file_url.url\" class=\"radius\" mode=\"widthFix\"></image>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<!-- 消息带图标 -->\n\t\t\t\t\t\t\t\t\t\t<view v-if=\"ite.type == 'text'\" class=\"cu-item self\">\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"main\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"content bg-green shadow\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle=\" border-top-left-radius: 30rpx; border-top-right-radius: 30rpx;  border-bottom-left-radius: 30rpx;\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span>{{ite.text}}</span>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view v-if='item.role === \"assistant\"' class=\"cu-item\">\n\t\t\t\t\t\t\t\t\t\t<!-- <towxml nodes=\"<h1>获取信息失败1</h1>\" /> -->\n\t\t\t\t\t\t\t\t\t\t<view class=\"main\" style=\"display: block;\">\n\t\t\t\t\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\t\t\t\t\tstyle=\"display:flex; align-items: center; justify-content: flex-start; padding-bottom: 15rpx;\">\n\t\t\t\t\t\t\t\t\t\t\t\t<image style=\"width: 50rpx; height: 50rpx;\" mode=\"aspectFit\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc=\"/static/images/dudu.gif\"></image>\n\t\t\t\t\t\t\t\t\t\t\t\t<!-- <view class=\"cu-avatar radius\" style=\"\"></view> -->\n\t\t\t\t\t\t\t\t\t\t\t\t<p style=\"color: #7e7e7e; padding-left: 20rpx; font-size: 26rpx;\">嘟嘟</p>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"content shadow\"\n\t\t\t\t\t\t\t\t\t\t\t\tstyle=\"display:block; border-top-right-radius: 30rpx; border-bottom-right-radius: 30rpx; border-bottom-left-radius: 30rpx;\">\n\t\t\t\t\t\t\t\t\t\t\t\t<!-- <view v-for=\"ite in item.content\">\n\t\t\t\t\t\t\t\t\t\t\t\t</view> -->\n\n\t\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"item.content[0].text\">\n\n\t\t\t\t\t\t\t\t\t\t\t\t\t<sapn v-for=\"ite in item.content\" :key=\"key\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle=\"margin-bottom: 20px; text-align-last:left; text-align:justify;\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<uaMarkdown :source=\"ite.text\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<!-- {{ite.text}} -->\n\t\t\t\t\t\t\t\t\t\t\t\t\t</sapn>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<view v-else>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"cu-load loading load-cuIcon  text-xxl padding-right-xl\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<!-- 两个小图标 -->\n\t\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"item.content[0].text\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle=\"width: 100%; display: flex; justify-content: flex-end;\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"iconBut\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<button @click=\"copySessionContent(item.content)\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"cuIcon-copy\"></view>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"iconBut\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<button @click=\"ShareSessionPoster(chatsList , index)\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"cuIcon-forward\"></view>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view style=\"height: 200rpx;\">\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\n\t\t\t<view>\n\t\t\t\t<!-- 文件上传弹窗 -->\n\t\t\t\t<view>\n\t\t\t\t\t<uni-popup ref=\"popup\" borderRadius=\"10px 10px 0 0\" background-color=\"#fff\">\n\t\t\t\t\t\t<view class=\"cu-list menu round \" style=\"color: #000;\">\n\t\t\t\t\t\t\t<view @tap=\"photograph\" class=\"cu-item\">拍照</view>\n\t\t\t\t\t\t\t<view @tap=\"chooseImage\" class=\"cu-item\">本地相册</view>\n\t\t\t\t\t\t\t<view @tap=\"openFile\" class=\"cu-item\">从微信聊天记录中选择</view>\n\t\t\t\t\t\t\t<view style=\"height: 10rpx; padding-bottom: 6rpx;\"></view>\n\t\t\t\t\t\t\t<view @tap=\"closeFileUpPopup\" class=\"cu-item\">取消</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</uni-popup>\n\t\t\t\t</view>\n\t\t\t\t<!-- 语音音阶动画 长按说话时的动画 -->\n\t\t\t\t<view v-if=\"animation\" style=\"z-index: 20; background: linear-gradient(to top, #FFFFFF, transparent); \n\t\t\t\t\tposition: fixed; bottom: 150rpx; width: 100%; height: 300rpx; padding-top: 150rpx; text-align: center; \">\n\t\t\t\t\t<view class=\"audioDiv\">\n\t\t\t\t\t\t<div class=\"audio\">\n\t\t\t\t\t\t\t<div class=\"wave\" v-for=\"item in 10\" :key=\"key\"></div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 底部输入框 -->\n\t\t\t\t<view style=\"position: fixed; width: 100%; bottom:0rpx; color: black; z-index: 20;\">\n\t\t\t\t\t<view class=\"cu-bar input\">\n\t\t\t\t\t\t<view @tap=\"characterInput=false\" v-if=\"characterInput\" class=\"action\">\n\t\t\t\t\t\t\t<text class=\"cuIcon-sound text-grey\"></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view @tap=\"characterInput=true\" v-else class=\"action\">\n\t\t\t\t\t\t\t<text class=\"cuIcon-keyboard text-grey\"></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<input v-if=\"characterInput\" v-model=\"userInput\" @focus=\"InputFocus\" @blur=\"InputBlur\"\n\t\t\t\t\t\t\tclass=\"solid-bottom\" :focus=\"false\" maxlength=\"300\" cursor-spacing=\"10\"></input>\n\t\t\t\t\t\t<button v-else @touchstart=\"streamRecord\" @touchend=\"endStreamRecord\" class=\"cu-btn\"\n\t\t\t\t\t\t\tstyle=\"width: 60%;\">按住说话</button>\n\t\t\t\t\t\t<view v-if=\"isNonFlow\" @tap=\"openFileUpPopup()\" class=\"action\">\n\t\t\t\t\t\t\t<text class=\"cuIcon-roundadd text-grey\"></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<button class=\"cu-btn bg-green \" :disabled=\"sandMsgButFlag\" type=\"\" @tap=\"sandMsg()\">\n\t\t\t\t\t\t\t<view style=\"width: 40rpx;\">\n\t\t\t\t\t\t\t\t<image style=\"width: 30rpx; height: 28rpx;\" src=\"../../static/images/icon/planeIcon.png\"\n\t\t\t\t\t\t\t\t\tmode=\"\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</button>\n\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 底部上传的文件展示 -->\n\t\t\t\t\t<view v-if=\"fileUp.show\" style=\"width: 100%; height: 90rpx; background-color: #FFFFFF;\"\n\t\t\t\t\t\tclass='cu-avatar radius'>\n\t\t\t\t\t\t<view style=\"width: 80%; display: inline-flex\">\n\t\t\t\t\t\t\t<view style=\"display: flex;\">\n\t\t\t\t\t\t\t\t<image class='cu-avatar radius' style=\"margin: 0rpx 2rpx; width: 90rpx; height: 90rpx;\"\n\t\t\t\t\t\t\t\t\tmode=\"scaleToFill\" :src=\"fileUp.tempFileUrl\">\n\t\t\t\t\t\t\t\t</image>\n\t\t\t\t\t\t\t\t<view style=\"position: relative; top: -30rpx; right: 30rpx;\">\n\t\t\t\t\t\t\t\t\t<uni-badge @tap=\"delTempFile\" style=\"padding: 10rpx;\" class=\"uni-badge-left-margin\"\n\t\t\t\t\t\t\t\t\t\ttext=\"X\" />\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view style=\" width: 100%; height: 50rpx; background-color: #FFFFFF;    z-index: 21;\">\n\t\t\t\t\t</view>\n\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<!--  关闭部分 -->\n\t\t<view class=\"DrawerClose\" :class=\"modalName=='viewModal'?'show':''\" @tap=\"hideModal\">\n\t\t\t<text class=\"cuIcon-pullright\"></text>\n\t\t</view>\n\t\t<!--  抽屉部分 -->\n\t\t<scroll-view scroll-y class=\"DrawerWindow\" :class=\"modalName=='viewModal'?'show':''\">\n\t\t\t<h2 style=\"text-align: center; color: black;\">历史会话</h2>\n\t\t\t<br>\n\t\t\t<view style=\"padding-top: 40rpx;\">\n\t\t\t\t<!-- 搜索框 -->\n\t\t\t\t<view style=\"width: 80%; margin: auto; \">\n\t\t\t\t\t<uni-search-bar cancelButton=\"none\" @input=\"monitorSessionSearchValue\" v-model=\"sessionSearchValue\"\n\t\t\t\t\t\tplaceholder=\"搜索\" radius=\"11\" bgColor=\"#EBEBF0\" />\n\t\t\t\t</view>\n\t\t\t\t<!-- 会话列表 -->\n\t\t\t\t<checkbox-group v-if=\"checkboxFlag\" @change=\"toggleCheckbox\">\n\t\t\t\t\t<view style=\"width: 80%; margin: auto;  font-family: 'Microsoft YaHei', sans-serif;\"\n\t\t\t\t\t\tv-for=\"item in sessionList\" :key=\"item.id\">\n\t\t\t\t\t\t<view style=\"display: flex; align-items: center; \">\n\t\t\t\t\t\t\t<p :data-index=\"item.id\" style=\"color: black; padding: 20rpx ; flex: 9;\"\n\t\t\t\t\t\t\t\t@tap=\"goOneSession(item)\" @longpress=\"onLongPress\">\n\t\t\t\t\t\t\t\t{{item.title}}\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t<checkbox style=\"transform:scale(0.7); flex: 1; \" :value=\"item.conversationId\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</checkbox-group>\n\n\t\t\t\t<view v-else style=\"width: 80%; margin: auto;  font-family: 'Microsoft YaHei', sans-serif;\"\n\t\t\t\t\tv-for=\"item in sessionList\" :key=\"item.id\">\n\t\t\t\t\t<view style=\"display: flex; align-items: center; \">\n\t\t\t\t\t\t<p :data-index=\"item.id\" style=\"color: black; padding: 20rpx ; flex: 9;\"\n\t\t\t\t\t\t\t@tap=\"goOneSession(item)\" @longpress=\"onLongPress\">\n\t\t\t\t\t\t\t{{item.title}}\n\t\t\t\t\t\t</p>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 编辑标题窗口 -->\n\t\t\t<view style=\"z-index: 21; width: 100%; height: 200rpx;\">\n\t\t\t\t<!-- 输入框示例 -->\n\t\t\t\t<uni-popup ref=\"inputDialog\" type=\"dialog\">\n\t\t\t\t\t<view class=\"cu-form-group\">\n\t\t\t\t\t\t<input placeholder=\"编辑标题\" name=\"input\" v-model=\"editWindowValue\"></input>\n\t\t\t\t\t\t<button @tap=\"editWindowConfirm\" class='cu-btn bg-green shadow'>确认</button>\n\t\t\t\t\t</view>\n\t\t\t\t\t<!-- <input class=\"uni-input\" :value=\"editWindowValue\" focus placeholder=\"编辑标题\" /> -->\n\t\t\t\t\t<!-- <uni-popup-dialog ref=\"inputClose\" mode=\"input\" title=\"编辑标题\" :value=\"editWindowValue\"\n\t\t\t\t\t\tplaceholder=\"请输入标题\" @confirm=\"editWindowConfirm\"></uni-popup-dialog> -->\n\t\t\t\t</uni-popup>\n\t\t\t</view>\n\t\t\t<!-- 删除会话确认框 -->\n\t\t\t<view>\n\t\t\t\t<!-- 提示窗示例 -->\n\t\t\t\t<uni-popup ref=\"alertDialog\" type=\"dialog\">\n\t\t\t\t\t<uni-popup-dialog type=\"info\" cancelText=\"取消\" confirmText=\"确定\" title=\"通知\" content=\"确定删除吗\"\n\t\t\t\t\t\t@confirm=\"delWindowConfirm\"></uni-popup-dialog>\n\t\t\t\t</uni-popup>\n\t\t\t</view>\n\n\t\t\t<!-- 小弹窗 -->\n\t\t\t<view class=\"shade\" v-show=\"showShade\" @tap=\"hidePop\">\n\t\t\t\t<view class=\"pop\" :style=\"popStyle\" :class=\"{'show':showPop}\">\n\t\t\t\t\t<view style=\"font-weight: 500; display: flex;align-items: center;\n\t\t\t\t\t\t\t\t\tjustify-content: center;\" v-for=\"(item,index) in popButton\" :key=\"index\" :data-index=\"index\">\n\t\t\t\t\t\t<view v-show=\"item.isLine\" style=\"color: #fff;\n\t\t\t\t\t\t            width: 2rpx;\n\t\t\t\t\t\t            height: 70rpx;\n\t\t\t\t\t\t\t\t\tpadding-right: 20rpx;\n\t\t\t\t\t\t\t\t\tborder-left: 3rpx solid #b5b5b5;\">\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view :class=\"item.color\" @tap=\"buttonFunction(item)\">\n\t\t\t\t\t\t\t<view style=\" width: 30rpx; height: 30rpx; margin:0rpx auto 4rpx; font-size: 30rpx;\"\n\t\t\t\t\t\t\t\t:class=\"item.icon\">\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<p style=\"padding: 2rpx;\">{{item.name}}</p>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view @tap=\"newSession\" style=\" position: fixed; bottom: 100rpx; width: 100%; text-align: center;\">\n\t\t\t\t<button class=\"cu-btn bg-green\" style=\"\"><text class=\"cuIcon-new\" style=\"padding-right: 10rpx;\"></text>\n\t\t\t\t\t<span>新会话</span>\n\t\t\t\t</button>\n\t\t\t</view>\n\n\t\t\t<!-- 多选确定部分 -->\n\t\t\t<view v-if=\"checkboxFlag\"\n\t\t\t\tstyle=\"width: 100%; height: 200rpx; background-color: #FFFFFF; z-index: 21; position: fixed; bottom: 0rpx;\">\n\t\t\t\t<view style=\"width: 90%; text-align: right;\">\n\t\t\t\t\t<button @tap=\"delMultipleSessions\" class=\"cu-btn bg-red margin-tb-sm\">删除</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</scroll-view>\n\t</view>\n</template>\n<script>\n\timport {\n\t\tgetUUId,\n\t\tchatV2Completions,\n\t\tgetUserChatHistory,\n\t\tgetChatHistoryDetail,\n\t\teditSessionTitle,\n\t\tdelSession,\n\t\tupLoadFile\n\t} from '@/api/dudu'\n\timport config from '@/config'\n\timport uaMarkdown from '@/components/ua2-markdown/ua-markdown.vue';\n\timport {\n\t\tgetToken\n\t} from '@/utils/auth'\n\tconst getUserId = () => {\n\t\treturn uni.getStorageSync('storage_data').vuex_userid;\n\t}\n\n\tconst baseUrl = config.baseUrl\n\tvar plugin = requirePlugin(\"WechatSI\")\n\tlet manager = plugin.getRecordRecognitionManager()\n\texport default {\n\t\tcomponents: {\n\t\t\tuaMarkdown\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tshareSession: {\n\t\t\t\t\tuserName: \"\",\n\t\t\t\t\tuser: \"\",\n\t\t\t\t\tassistant: \"\"\n\t\t\t\t},\n\t\t\t\t// true: 流式接口    false: 非流式接口\n\t\t\t\t// isNonFlow: true,\n\t\t\t\tisNonFlow: getApp().globalData.config.appInfo.isNonFlow,\n\t\t\t\tsandMsgButFlag: false,\n\t\t\t\tscrollTop: 0, //滚动条位置\n\t\t\t\tscrollViewHeight: 700, //滚动视图的高度\n\t\t\t\thtml: '',\n\t\t\t\tfileUp: {\n\t\t\t\t\tshow: false,\n\t\t\t\t\ttempFileUrl: \"\",\n\t\t\t\t\ttype: \"\"\n\t\t\t\t},\n\t\t\t\tcheckboxFlag: false,\n\t\t\t\tCheckboxTempData: [],\n\t\t\t\tchooseIndex: -1,\n\t\t\t\teditWindowValue: \"\",\n\t\t\t\tchatsList: [],\n\t\t\t\t//发送的消息\n\t\t\t\tuserInput: \"\",\n\t\t\t\t// 会话ID\n\t\t\t\tconversationId: \"\",\n\t\t\t\tfileUrl: \"\",\n\t\t\t\t// 1 普通 2 解析文件\n\t\t\t\tchatType: \"1\",\n\t\t\t\t// 小弹窗\n\t\t\t\tuserList: [],\n\t\t\t\t/* 窗口尺寸 */\n\t\t\t\twinSize: {},\n\t\t\t\t/* 显示遮罩 */\n\t\t\t\tshowShade: false,\n\t\t\t\t/* 显示操作弹窗 */\n\t\t\t\tshowPop: false,\n\t\t\t\t/* 弹窗按钮列表 */\n\t\t\t\tpopButton: [{\n\t\t\t\t\t\tindex: 0,\n\t\t\t\t\t\tname: \"编辑标题\",\n\t\t\t\t\t\ticon: \"cuIcon-edit\"\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tindex: 1,\n\t\t\t\t\t\tname: \"删除会话\",\n\t\t\t\t\t\ticon: \"cuIcon-delete\",\n\t\t\t\t\t\tcolor: \"redLogoAndTitle\"\n\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tindex: 2,\n\t\t\t\t\t\tname: \"多选\",\n\t\t\t\t\t\ticon: \"cuIcon-squarecheck\",\n\t\t\t\t\t\tisLine: true\n\t\t\t\t\t}\n\n\t\t\t\t],\n\t\t\t\tsessionSearchValue: '',\n\t\t\t\t/* 弹窗定位样式 */\n\t\t\t\tpopStyle: \"\",\n\t\t\t\t/* 选择的用户下标 */\n\t\t\t\tpickerUserIndex: -1,\n\n\n\t\t\t\tanimation: false,\n\t\t\t\tcharacterInput: true,\n\t\t\t\tInputBottom: 0,\n\t\t\t\tsessionList: [{\n\t\t\t\t\t\tname: \"会话1\"\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: \"会话2\"\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: \"会话3\"\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: \"会话4\"\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: \"会话5\"\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: \"会话6\"\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tmodalName: null,\n\n\t\t\t\tstatusBarHeight: uni.getStorageSync('menuInfo').statusBarHeight, //状态栏的高度（可以设置为顶部导航条的padding-top）\n\t\t\t\tmenuWidth: uni.getStorageSync('menuInfo').menuWidth,\n\t\t\t\tmenuHeight: uni.getStorageSync('menuInfo').menuHeight,\n\t\t\t\tmenuBorderRadius: uni.getStorageSync('menuInfo').menuBorderRadius,\n\t\t\t\tmenuRight: uni.getStorageSync('menuInfo').menuRight,\n\t\t\t\tmenuTop: uni.getStorageSync('menuInfo').menuTop,\n\t\t\t\tcontentTop: uni.getStorageSync('menuInfo').contentTop,\n\t\t\t}\n\t\t},\n\t\tmounted() {},\n\t\tonLoad() {\n\t\t\tthis.initRecord();\n\t\t\tthis.getUserChatHistory();\n\t\t},\n\t\tonShow() {\n\t\t\tconst res = uni.getSystemInfoSync();\n\t\t\tthis.scrollViewHeight = res.windowHeight - 150;\n\n\t\t\tthis.getUserName();\n\t\t},\n\t\tmethods: {\n\t\t\t// 获取用户名\n\t\t\tgetUserName() {\n\t\t\t\tlet obj = uni.getStorageSync('storage_data');\n\t\t\t\t// console.log('obj: ',obj);\n\t\t\t\tthis.shareSession.userName = obj.vuex_name;\n\t\t\t},\n\t\t\t// 移除 Markdown 语法，如标题、加粗、斜体等\n\t\t\tmarkdownToPlainText(markdown) {\n\t\t\t\tlet plainText = markdown\n\t\t\t\t\t.replace(/#+\\s*/g, '') // 移除标题符号和空格\n\t\t\t\t\t.replace(/<br\\s*\\/?>/gi, '\\n') // 将 <br> 标签替换为换行符\n\t\t\t\t\t.replace(/\\*\\*(.*?)\\*\\*/g, '$1') // 移除加粗\n\t\t\t\t\t.replace(/\\*(.*?)\\*/g, '$1') // 移除斜体\n\t\t\t\t\t.replace(/~~(.*?)~~/g, '$1') // 移除删除线\n\t\t\t\t\t.replace(/`(.*?)`/g, '$1') // 移除代码块\n\t\t\t\t\t.replace(/\\[(.*?)\\]\\((.*?)\\)/g, '$1') // 移除链接\n\t\t\t\t\t.replace(/!\\[(.*?)\\]\\((.*?)\\)/g, '$1') // 移除图片\n\t\t\t\t\t.replace(/\\n+/g, '\\n') // 将多个换行符替换为单个换行符\n\t\t\t\t\t.replace(/^\\s+|\\s+$/g, ''); // 移除首尾空白字符\n\n\t\t\t\treturn plainText;\n\t\t\t},\n\t\t\t// 生成海报 分享对话\n\t\t\tShareSessionPoster(chatsList, index) {\n\t\t\t\t// console.log('adb ', chatsList[index].content[0].text);\n\t\t\t\t// console.log('chatsList[index].content[0].text: ', chatsList[index].content[0].text);\n\t\t\t\tlet tempStr = this.markdownToPlainText(chatsList[index].content[0].text);\n\t\t\t\t// console.log('tempStr: ', tempStr);\n\t\t\t\tthis.shareSession.user = chatsList[index - 1].content[0].text\n\t\t\t\tthis.shareSession.assistant = tempStr + \" \";\n\t\t\t\tthis.$forceUpdate()\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tthis.renderGeneratedPicture();\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 渲染 生成图片\n\t\t\trenderGeneratedPicture() {\n\t\t\t\t// 渲染\n\t\t\t\tthis.$refs.painter.canvasToTempFilePathSync({\n\t\t\t\t\tfileType: \"jpg\",\n\t\t\t\t\t// 如果返回的是base64是无法使用 saveImageToPhotosAlbum，需要设置 pathType为url\n\t\t\t\t\tpathType: 'url',\n\t\t\t\t\tquality: 1,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t// console.log(res.tempFilePath);\n\t\t\t\t\t\twx.showShareImageMenu({\n\t\t\t\t\t\t\tpath: res.tempFilePath\n\t\t\t\t\t\t})\n\t\t\t\t\t},\n\t\t\t\t\tfail: (res) => {\n\t\t\t\t\t\t// console.log('res: ', res);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 复制会话内容\n\t\t\tcopySessionContent(data) {\n\t\t\t\t// console.log('data: ', data);\n\t\t\t\tuni.setClipboardData({\n\t\t\t\t\tdata: data[0].text,\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '复制成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('复制失败', err);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '复制失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 滚动到底部\n\t\t\tscrollToBottom() {\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tuni.createSelectorQuery().in(this).select('#scroll-view-content').boundingClientRect((res) => {\n\t\t\t\t\t\t// // console.log('res: ', res);\n\t\t\t\t\t\tlet top = res.height - this.scrollViewHeight + 12000;\n\t\t\t\t\t\tif (top > 0) {\n\t\t\t\t\t\t\tthis.scrollTop = top;\n\t\t\t\t\t\t}\n\t\t\t\t\t}).exec()\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 监测搜索输入框\n\t\t\tmonitorSessionSearchValue(e) {\n\t\t\t\t// console.log('e: ', e);\n\t\t\t\tthis.sessionSearch(e);\n\t\t\t},\n\t\t\t// 会话搜索框\n\t\t\tsessionSearch(e) {\n\t\t\t\tlet _this = this;\n\t\t\t\tgetUserChatHistory({\n\t\t\t\t\ttitle: e\n\t\t\t\t}).then(res => {\n\t\t\t\t\t// console.log('res: ', res);\n\t\t\t\t\tres.data.map((item, index) => {\n\t\t\t\t\t\titem.id = index\n\t\t\t\t\t})\n\t\t\t\t\t_this.sessionList = res.data;\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 多选\n\t\t\ttoggleCheckbox(e) {\n\t\t\t\t// console.log('e: ', e);\n\t\t\t\tthis.CheckboxTempData = e.detail.value;\n\t\t\t},\n\t\t\t// 删除多个会话\n\t\t\tdelMultipleSessions() {\n\t\t\t\tlet _this = this;\n\t\t\t\tif (this.CheckboxTempData.length > 0) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\tcontent: '删除选中的' + this.CheckboxTempData.length + '个会话',\n\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tlet tempArr = [];\n\t\t\t\t\t\t\t\t_this.CheckboxTempData.map(item => {\n\t\t\t\t\t\t\t\t\ttempArr.push(item)\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\tdelSession(tempArr).then(res => {\n\t\t\t\t\t\t\t\t\t// console.log('res: ', res);\n\t\t\t\t\t\t\t\t\tif (res.code == 200) {\n\t\t\t\t\t\t\t\t\t\t_this.$refs.alertDialog.close();\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t_this.checkboxFlag = false;\n\t\t\t\t\t\t\t\t\t_this.getUserChatHistory();\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\t\t// console.log('用户点击取消');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请先勾选会话',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 删除临时文件\n\t\t\tdelTempFile() {\n\t\t\t\tthis.fileUp.show = false;\n\t\t\t\tthis.fileUp.tempFileUrl = \"\";\n\t\t\t},\n\t\t\t// 小窗按钮\n\t\t\tbuttonFunction(data) {\n\t\t\t\t// 0:编辑标题 1:删除会话 2:多选\n\t\t\t\t// console.log('data: ', data);\n\t\t\t\t// console.log('this.pickerUserIndex: ', this.pickerUserIndex);\n\t\t\t\tif (data.index == 0) {\n\t\t\t\t\tthis.editWindowValue = this.sessionList[this.pickerUserIndex].title + \"\";\n\t\t\t\t\tthis.$refs.inputDialog.open()\n\t\t\t\t} else if (data.index == 1) {\n\n\t\t\t\t\tthis.$refs.alertDialog.open()\n\n\t\t\t\t} else if (data.index == 2) {\n\n\t\t\t\t\tthis.checkboxFlag = true;\n\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 编辑标题 确认按钮\n\t\t\teditWindowConfirm() {\n\t\t\t\t// // console.log('this.sessionList: ', this.sessionList);\n\t\t\t\t// // console.log('this.pickerUserIndex: ', this.pickerUserIndex);\n\t\t\t\t// // console.log('this: ', this.sessionList[this.pickerUserIndex]);\n\t\t\t\t// // console.log('this.editWindowValue: ', this.editWindowValue);\n\t\t\t\teditSessionTitle({\n\t\t\t\t\t\"conversationId\": this.sessionList[this.chooseIndex].conversationId,\n\t\t\t\t\t\"title\": this.editWindowValue\n\t\t\t\t}).then(res => {\n\t\t\t\t\t// console.log('res: ', res);\n\t\t\t\t\tif (res.code == 200) {\n\t\t\t\t\t\tthis.$refs.inputDialog.close();\n\t\t\t\t\t}\n\t\t\t\t\tthis.getUserChatHistory();\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 删除一个会话\n\t\t\tdelWindowConfirm() {\n\t\t\t\tdelSession([\n\t\t\t\t\tthis.sessionList[this.chooseIndex].conversationId\n\t\t\t\t]).then(res => {\n\t\t\t\t\t// console.log('res: ', res);\n\t\t\t\t\tif (res.code == 200) {\n\t\t\t\t\t\tthis.$refs.alertDialog.close();\n\t\t\t\t\t}\n\t\t\t\t\tthis.getUserChatHistory();\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 跳转到历史会话\n\t\t\tgoOneSession(item) {\n\t\t\t\t// // console.log('item: ', item);\n\t\t\t\tthis.sessionSearchValue = '';\n\t\t\t\tthis.conversationId = item.conversationId;\n\t\t\t\tthis.getChatHistoryDetail();\n\t\t\t\tthis.hideModal();\n\t\t\t},\n\t\t\t// 新会话\n\t\t\tnewSession() {\n\t\t\t\tthis.conversationId = getUUId();\n\t\t\t\tthis.chatsList = [];\n\t\t\t\tthis.hideModal();\n\t\t\t},\n\t\t\t// 发送消息\n\t\t\tsandMsg() {\n\t\t\t\t// console.log('this.userInput: ', this.userInput);\n\t\t\t\tif (this.userInput.length > 0) {\n\t\t\t\t\t//如果是流式接口  isNonFlow  true:流式   false:非流式\n\t\t\t\t\tif (this.isNonFlow) {\n\t\t\t\t\t\t// 有上传的文件\n\t\t\t\t\t\tif (this.fileUp.show) {\n\t\t\t\t\t\t\tlet tempObj = {\n\t\t\t\t\t\t\t\t\"role\": \"user\",\n\t\t\t\t\t\t\t\t\"content\": [{\n\t\t\t\t\t\t\t\t\t\t\"text\": this.userInput,\n\t\t\t\t\t\t\t\t\t\t\"type\": \"text\"\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\"file_url\": {\n\t\t\t\t\t\t\t\t\t\t\t\"type\": this.fileUp.type,\n\t\t\t\t\t\t\t\t\t\t\t\"url\": this.fileUp.tempFileUrl\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\"type\": \"file_url\"\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t]\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tthis.chatsList.push(tempObj);\n\n\t\t\t\t\t\t\tthis.chatCompletions(this.fileUp.tempFileUrl, 2);\n\n\t\t\t\t\t\t\tthis.userInput = \"\";\n\t\t\t\t\t\t\tthis.fileUp.show = false;\n\t\t\t\t\t\t\tthis.sandMsgButFlag = true;\n\n\t\t\t\t\t\t\tthis.scrollToBottom()\n\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tlet tempObj = {\n\t\t\t\t\t\t\t\t\"role\": \"user\",\n\t\t\t\t\t\t\t\t\"content\": [{\n\t\t\t\t\t\t\t\t\t\"text\": this.userInput,\n\t\t\t\t\t\t\t\t\t\"type\": \"text\"\n\t\t\t\t\t\t\t\t}]\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tthis.chatsList.push(tempObj);\n\n\t\t\t\t\t\t\tthis.chatCompletions(\"\", 1);\n\n\t\t\t\t\t\t\tthis.userInput = \"\";\n\n\t\t\t\t\t\t\tthis.sandMsgButFlag = true;\n\t\t\t\t\t\t\tthis.scrollToBottom()\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tlet tempObj = {\n\t\t\t\t\t\t\t\"role\": \"user\",\n\t\t\t\t\t\t\t\"content\": [{\n\t\t\t\t\t\t\t\t\"text\": this.userInput,\n\t\t\t\t\t\t\t\t\"type\": \"text\"\n\t\t\t\t\t\t\t}]\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tthis.chatsList.push(tempObj);\n\n\t\t\t\t\t\tthis.chatV2Completions();\n\n\t\t\t\t\t\tthis.userInput = \"\";\n\n\t\t\t\t\t\tthis.sandMsgButFlag = true;\n\t\t\t\t\t\tthis.scrollToBottom()\n\t\t\t\t\t}\n\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync getUserChatHistory() {\n\t\t\t\tconst res = await getUserChatHistory({\n\t\t\t\t\ttitle: ''\n\t\t\t\t}).then()\n\t\t\t\t// // console.log('res: ', res);\n\t\t\t\tif (res.code == 200) {\n\t\t\t\t\tres.data.map((item, index) => {\n\t\t\t\t\t\titem.id = index\n\t\t\t\t\t})\n\t\t\t\t\tthis.sessionList = res.data;\n\t\t\t\t\t// console.log('this.sessionList: ', this.sessionList);\n\n\n\t\t\t\t\tif (res.data.length == 0) {\n\t\t\t\t\t\tthis.conversationId = getUUId();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.conversationId = res.data.at(-1).conversationId\n\t\t\t\t\t\t// // console.log('this.conversationId: ', this.conversationId);\n\t\t\t\t\t\t// // console.log('1: ', new Date().getTime());\n\t\t\t\t\t}\n\t\t\t\t\tthis.getChatHistoryDetail();\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 历史会话列表\n\t\t\tasync getChatHistoryDetail() {\n\t\t\t\t// // console.log('2: ', new Date().getTime());\n\t\t\t\tawait getChatHistoryDetail({\n\t\t\t\t\tconversationId: this.conversationId\n\t\t\t\t}).then(res => {\n\t\t\t\t\t// // console.log('res: ', res);\n\t\t\t\t\tif (res.code == 200) {\n\t\t\t\t\t\tif (res.data != null) {\n\t\t\t\t\t\t\tif (res.data.length == 0) {\n\t\t\t\t\t\t\t\tthis.chatsList = []\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.chatsList = res.data\n\t\t\t\t\t\t\t\t// // console.log('this.chatsList: ', this.chatsList);\n\t\t\t\t\t\t\t\tthis.scrollToBottom();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.chatsList = []\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 会话 - 非流式\n\t\t\tasync chatV2Completions() {\n\t\t\t\tlet _this = this;\n\n\t\t\t\tlet params = {\n\t\t\t\t\tuserPrompt: this.userInput,\n\t\t\t\t\tconversationId: this.conversationId,\n\t\t\t\t}\n\n\t\t\t\tlet endIndex = _this.chatsList.length - 1;\n\t\t\t\tlet lastElement = _this.chatsList[endIndex];\n\n\t\t\t\tif (lastElement.role == \"user\") {\n\t\t\t\t\tlet tempObj = {\n\t\t\t\t\t\t\"role\": \"assistant\",\n\t\t\t\t\t\t\"content\": [{\n\t\t\t\t\t\t\t\"text\": \"\",\n\t\t\t\t\t\t\t\"type\": \"text\"\n\t\t\t\t\t\t}]\n\t\t\t\t\t}\n\t\t\t\t\t_this.chatsList.push(tempObj);\n\t\t\t\t}\n\t\t\t\tlet res = await chatV2Completions(params).then();\n\t\t\t\t// console.log('res: ', res);\n\t\t\t\tif (res.code == 200) {\n\t\t\t\t\t_this.chatsList[_this.chatsList.length - 1].content[0]\n\t\t\t\t\t\t.text = res.data;\n\t\t\t\t\t_this.sandMsgButFlag = false;\n\t\t\t\t\t_this.scrollToBottom()\n\t\t\t\t}\n\n\n\t\t\t},\n\t\t\t// 会话请求-流式\n\t\t\tchatCompletions(tempFileUrl, chatType) {\n\t\t\t\tlet _this = this;\n\n\t\t\t\tlet params = {\n\t\t\t\t\tuserPrompt: this.userInput,\n\t\t\t\t\tconversationId: this.conversationId,\n\t\t\t\t\tfileUrl: tempFileUrl,\n\t\t\t\t\t// 1 普通 2 解析文件\n\t\t\t\t\tchatType: chatType,\n\t\t\t\t}\n\n\t\t\t\tconst requestTask = uni.request({\n\t\t\t\t\turl: baseUrl +\n\t\t\t\t\t\t`/mini/chat/completions?userPrompt=${params.userPrompt}&conversationId=${params.conversationId}&userId=${getUserId()}&fileUrl=${params.fileUrl}&chatType=${params.chatType}`, // 请求地址\n\t\t\t\t\ttimeout: 120000,\n\t\t\t\t\tresponseType: 'text',\n\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\tenableChunked: true, //配置这里\t\t\n\t\t\t\t\tsuccess: response => {},\n\t\t\t\t\tfail: error => {}\n\t\t\t\t})\n\t\t\t\trequestTask.onHeadersReceived(function(res) {});\n\t\t\t\t// 这里监听消息\n\t\t\t\trequestTask.onChunkReceived(function(res) {\n\t\t\t\t\t// 小程序报错写法\n\t\t\t\t\t// let decoder = new TextDecoder('utf-8');\n\t\t\t\t\t// let text = decoder.decode(new Uint8Array(res.data));\n\n\t\t\t\t\t// 兼容写法\n\t\t\t\t\tlet byteArray = Array.from(new Uint8Array(res.data));\n\t\t\t\t\tlet str = String.fromCharCode(...byteArray);\n\t\t\t\t\tlet escapedStr = escape(str);\n\t\t\t\t\t// 使用 decodeURIComponent 解码字符串\n\t\t\t\t\tlet text = decodeURIComponent(escapedStr);\n\n\t\t\t\t\tlet endIndex = _this.chatsList.length - 1;\n\t\t\t\t\tlet lastElement = _this.chatsList[endIndex];\n\n\t\t\t\t\tif (lastElement.role == \"user\") {\n\t\t\t\t\t\tlet tempObj = {\n\t\t\t\t\t\t\t\"role\": \"assistant\",\n\t\t\t\t\t\t\t\"content\": [{\n\t\t\t\t\t\t\t\t\"text\": \"\",\n\t\t\t\t\t\t\t\t\"type\": \"text\"\n\t\t\t\t\t\t\t}]\n\t\t\t\t\t\t}\n\t\t\t\t\t\t_this.chatsList.push(tempObj);\n\t\t\t\t\t}\n\n\t\t\t\t\t// 使用换行符分割字符串\n\t\t\t\t\tconst jsonArray = text.split('\\n');\n\n\t\t\t\t\t// 遍历数组并解析每个元素为JSON对象\n\t\t\t\t\tjsonArray.forEach(item => {\n\t\t\t\t\t\tlet contentText = \"\";\n\t\t\t\t\t\tif (item.startsWith('data:')) {\n\t\t\t\t\t\t\t// console.log('item: ', item);\n\t\t\t\t\t\t\tconst jsonString = item.slice(5); // 去掉 'data: ' 前缀\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\tif (jsonString == \"[DONE]\") {\n\t\t\t\t\t\t\t\t\t_this.sandMsgButFlag = false;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (jsonString != \"[DONE]\") {\n\t\t\t\t\t\t\t\t\tif (jsonString.length > 0) {\n\t\t\t\t\t\t\t\t\t\tconst jsonObject = JSON.parse(jsonString);\n\t\t\t\t\t\t\t\t\t\t// // console.log(jsonObject);\n\t\t\t\t\t\t\t\t\t\tif (jsonObject.choices[0].delta.role == \"assistant\") {\n\t\t\t\t\t\t\t\t\t\t\tcontentText = jsonObject.choices[0].delta.content;\n\t\t\t\t\t\t\t\t\t\t\t_this.chatsList[_this.chatsList.length - 1].content[0].text +=\n\t\t\t\t\t\t\t\t\t\t\t\tcontentText;\n\t\t\t\t\t\t\t\t\t\t\t_this.scrollToBottom()\n\t\t\t\t\t\t\t\t\t\t} else if (jsonObject.choices[0].delta.role == \"tool\") {\n\t\t\t\t\t\t\t\t\t\t\tcontentText = jsonObject.choices[0].delta.content;\n\t\t\t\t\t\t\t\t\t\t\tif (contentText != \"\") {\n\t\t\t\t\t\t\t\t\t\t\t\t_this.chatsList[_this.chatsList.length - 1].content[0]\n\t\t\t\t\t\t\t\t\t\t\t\t\t.text = contentText;\n\t\t\t\t\t\t\t\t\t\t\t\t_this.scrollToBottom()\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\t\tconsole.error(\"Error parsing JSON string:\", error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t})\n\t\t\t},\n\t\t\t/* 长按监听 */\n\t\t\tonLongPress(e) {\n\t\t\t\tlet [touches, style, index] = [e.touches[0], \"\", e.currentTarget.dataset.index];\n\n\t\t\t\t/* 因 非H5端不兼容 style 属性绑定 Object ，所以拼接字符 */\n\t\t\t\tif (touches.clientY > (this.winSize.height / 2)) {\n\t\t\t\t\tstyle = `bottom:${this.winSize.height-touches.clientY}px;`;\n\t\t\t\t} else {\n\t\t\t\t\tstyle = `top:${touches.clientY}px;`;\n\t\t\t\t}\n\t\t\t\tif (touches.clientX > (this.winSize.witdh / 2)) {\n\t\t\t\t\tstyle += `right:${this.winSize.witdh-touches.clientX}px`;\n\t\t\t\t} else {\n\t\t\t\t\tstyle += `left:${touches.clientX}px`;\n\t\t\t\t}\n\n\t\t\t\tthis.popStyle = style;\n\t\t\t\tthis.pickerUserIndex = Number(index);\n\t\t\t\tthis.chooseIndex = Number(index);\n\t\t\t\tthis.showShade = true;\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.showPop = true;\n\t\t\t\t\t}, 10);\n\t\t\t\t});\n\t\t\t},\n\t\t\t/* 隐藏弹窗 */\n\t\t\thidePop() {\n\t\t\t\tthis.showPop = false;\n\t\t\t\tthis.pickerUserIndex = -1;\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.showShade = false;\n\t\t\t\t}, 250);\n\t\t\t},\n\t\t\t// 打开文件上传弹窗\n\t\t\topenFileUpPopup() {\n\t\t\t\tthis.$refs.popup.open('bottom')\n\t\t\t},\n\t\t\t// 拍照\n\t\t\tphotograph() {\n\t\t\t\tthis.closeFileUpPopup();\n\n\t\t\t\tvar _this = this;\n\t\t\t\tuni.chooseImage({\n\t\t\t\t\tcount: 1,\n\t\t\t\t\tsizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有\n\t\t\t\t\tsourceType: ['camera'], // 可以指定来源是相册还是相机，默认二者都有\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t_this.uploadFiles(res.tempFiles[0], \"image\");\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('选择图片失败:', err);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 本地照片\n\t\t\tchooseImage() {\n\t\t\t\tthis.closeFileUpPopup();\n\n\t\t\t\tvar _this = this;\n\t\t\t\tuni.chooseImage({\n\t\t\t\t\tcount: 1,\n\t\t\t\t\tsizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有\n\t\t\t\t\tsourceType: ['album'], // 可以指定来源是相册还是相机，默认二者都有\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t// console.log('成功选择图片:', res);\n\t\t\t\t\t\t_this.uploadFiles(res.tempFiles[0], \"image\");\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('选择图片失败:', err);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 从微信聊天记录中选择\n\t\t\topenFile() {\n\t\t\t\tthis.closeFileUpPopup();\n\n\t\t\t\tvar _this = this;\n\t\t\t\twx.chooseMessageFile({\n\t\t\t\t\tcount: 1,\n\t\t\t\t\ttype: 'all',\n\t\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\t\t// console.log('res: ', res);\n\t\t\t\t\t\t_this.uploadFiles(res.tempFiles[0], tempFile.type);\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t// 用户取消选择文件或选择文件失败的回调函数\n\t\t\t\t\t\tthrow err;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 取消  关闭弹窗\n\t\t\tcloseFileUpPopup() {\n\t\t\t\tthis.$refs.popup.close();\n\t\t\t},\n\t\t\t// 文件上传\n\t\t\tuploadFiles(tempFile, fileType) {\n\t\t\t\t// console.log('tempFile: ', tempFile);\n\t\t\t\tlet _this = this;\n\t\t\t\tuni.uploadFile({\n\t\t\t\t\turl: baseUrl + '/common/upload',\n\t\t\t\t\tfilePath: tempFile.path,\n\t\t\t\t\tname: 'file',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t// console.log(res.data);\n\t\t\t\t\t\tlet jsonRes = JSON.parse(res.data)\n\t\t\t\t\t\tif (jsonRes.code == 200) {\n\t\t\t\t\t\t\t_this.fileUp.show = true;\n\t\t\t\t\t\t\t_this.fileUp.type = tempFile.type;\n\t\t\t\t\t\t\t_this.fileUp.tempFileUrl = jsonRes.url;\n\n\t\t\t\t\t\t\tif (fileType == \"image\") {\n\t\t\t\t\t\t\t\t_this.userInput = \"描述一下图片内容\";\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 语音识别\n\t\t\tstreamRecord: function() {\n\t\t\t\t// console.log('开始')\n\t\t\t\tthis.animation = true;\n\t\t\t\tmanager.start({\n\t\t\t\t\tlang: 'zh_CN',\n\t\t\t\t})\n\t\t\t},\n\t\t\tendStreamRecord: function() {\n\t\t\t\tthis.animation = false;\n\t\t\t\t// console.log('结束')\n\t\t\t\tmanager.stop()\n\t\t\t},\n\t\t\tinitRecord: function() {\n\t\t\t\t//有新的识别内容返回，则会调用此事件\n\t\t\t\tmanager.onRecognize = (res) => {\n\t\t\t\t\tlet text = res.result\n\t\t\t\t\tthis.userInput = text\n\t\t\t\t}\n\t\t\t\t// 识别结束事件\n\t\t\t\tmanager.onStop = (res) => {\n\t\t\t\t\t// console.log(res, 37);\n\t\t\t\t\tlet text = res.result\n\t\t\t\t\tif (text == '') {\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\tthis.userInput = text\n\t\t\t\t\tthis.sandMsg();\n\t\t\t\t\tthis.userInput = \"\"\n\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tInputFocus(e) {\n\t\t\t\tthis.InputBottom = e.detail.height\n\t\t\t},\n\t\t\tInputBlur(e) {\n\t\t\t\tthis.InputBottom = 0\n\t\t\t},\n\t\t\tshowModal(e) {\n\t\t\t\t// console.log(e)\n\t\t\t\tthis.modalName = e.currentTarget.dataset.target\n\t\t\t\t// 获取会话列表\n\t\t\t\tthis.getUserChatHistory();\n\t\t\t},\n\t\t\t// 关闭抽屉\n\t\t\thideModal(e) {\n\t\t\t\tthis.modalName = null;\n\t\t\t\tthis.checkboxFlag = false;\n\t\t\t},\n\t\t\ttabSelect(e) {\n\t\t\t\tthis.TabCur = e.currentTarget.dataset.id;\n\t\t\t\tthis.scrollLeft = (e.currentTarget.dataset.id - 1) * 60\n\t\t\t},\n\t\t\tgoToBack() {\n\t\t\t\tuni.navigateBack({\n\t\t\t\t\tdelta: 1\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style lang=\"less\" scope>\n\tpage {\n\t\tbackground-image: var(--gradualBlue);\n\t\twidth: 100vw;\n\t\toverflow: hidden;\n\t}\n\n\t.iconBut button {\n\t\tbackground-color: transparent;\n\t}\n\n\t.iconBut button::after {\n\t\tborder: none;\n\t}\n\n\n\t.cu-load {\n\t\tline-height: 25rpx;\n\t\theight: 25rpx;\n\t}\n\n\t.redLogoAndTitle {\n\t\tcolor: #E06256;\n\t}\n\n\t.cu-list.menu>.cu-item {\n\t\tjustify-content: center;\n\t}\n\n\t// 聊天对话框\n\t.cu-chat,\n\t.cu-item,\n\t.main {\n\t\tz-index: 1;\n\t}\n\n\t// 录音动画\n\t.audioDiv {\n\t\tdisplay: grid;\n\t\tplace-items: center;\n\n\t\t.audio {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tgap: 8px;\n\t\t\twidth: 110px;\n\t\t\theight: 40px;\n\n\t\t\t.wave {\n\t\t\t\theight: 40px;\n\t\t\t\tdisplay: block;\n\t\t\t\twidth: 10px;\n\t\t\t\theight: 6px;\n\t\t\t\tborder-radius: 8px;\n\t\t\t\tbackground: orange;\n\t\t\t}\n\t\t}\n\n\t\t.audio {\n\t\t\t.wave {\n\t\t\t\tanimation: audio-wave 1.3s ease-in-out infinite;\n\n\t\t\t\t&:nth-child(1) {\n\t\t\t\t\tanimation-delay: 0.1s;\n\t\t\t\t}\n\n\t\t\t\t&:nth-child(2) {\n\t\t\t\t\tanimation-delay: 0.2s;\n\t\t\t\t}\n\n\t\t\t\t&:nth-child(3) {\n\t\t\t\t\tanimation-delay: 0.3s;\n\t\t\t\t}\n\n\t\t\t\t&:nth-child(4) {\n\t\t\t\t\tanimation-delay: 0.4s;\n\t\t\t\t}\n\n\t\t\t\t&:nth-child(5) {\n\t\t\t\t\tanimation-delay: 0.5s;\n\t\t\t\t}\n\n\t\t\t\t&:nth-child(6) {\n\t\t\t\t\tanimation-delay: 0.6s;\n\t\t\t\t}\n\n\t\t\t\t&:nth-child(7) {\n\t\t\t\t\tanimation-delay: 0.7s;\n\t\t\t\t}\n\n\t\t\t\t&:nth-child(8) {\n\t\t\t\t\tanimation-delay: 0.8s;\n\t\t\t\t}\n\n\t\t\t\t&:nth-child(9) {\n\t\t\t\t\tanimation-delay: 0.9s;\n\t\t\t\t}\n\n\t\t\t\t&:nth-child(10) {\n\t\t\t\t\tanimation-delay: 1s;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t@keyframes audio-wave {\n\t\t\t0% {\n\t\t\t\theight: 6px;\n\t\t\t\ttransform: translateY(0px);\n\t\t\t\tbackground: #ff8e3a;\n\t\t\t}\n\n\t\t\t25% {\n\t\t\t\theight: 6px;\n\t\t\t\ttransform: translateY(0px);\n\t\t\t\tbackground: #9c73f8;\n\t\t\t}\n\n\t\t\t50% {\n\t\t\t\theight: 30px;\n\t\t\t\ttransform: translateY(-5px) scaleY(1.5);\n\t\t\t\tbackground: #ed509e;\n\t\t\t}\n\n\t\t\t75% {\n\t\t\t\theight: 6px;\n\t\t\t\ttransform: translateY(0px);\n\t\t\t\tbackground: #9c73f8;\n\t\t\t}\n\n\t\t\t100% {\n\t\t\t\theight: 6px;\n\t\t\t\ttransform: translateY(0px);\n\t\t\t\tbackground: #0fccce;\n\t\t\t}\n\t\t}\n\t}\n\n\n\t// 小弹窗\n\t.shade {\n\t\tposition: fixed;\n\t\tz-index: 100;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\t-webkit-touch-callout: none;\n\n\t\t.pop {\n\t\t\tborder-radius: 18rpx;\n\t\t\tposition: fixed;\n\t\t\tz-index: 101;\n\t\t\tdisplay: flex;\n\t\t\twidth: 350upx;\n\t\t\tbox-sizing: border-box;\n\t\t\tfont-size: 24upx;\n\t\t\ttext-align: left;\n\t\t\tcolor: #333;\n\t\t\tbackground-color: #fff;\n\t\t\tbox-shadow: 0 0 5px rgba(0, 0, 0, 0.5);\n\t\t\tline-height: 80upx;\n\t\t\ttransition: transform 0.15s ease-in-out 0s;\n\t\t\tuser-select: none;\n\t\t\t-webkit-touch-callout: none;\n\t\t\ttransform: scale(0, 0);\n\n\t\t\t&.show {\n\t\t\t\ttransform: scale(1, 1);\n\t\t\t}\n\n\t\t\t&>view {\n\t\t\t\tpadding: 0 20upx;\n\t\t\t\toverflow: hidden;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\tuser-select: none;\n\t\t\t\t-webkit-touch-callout: none;\n\n\t\t\t\t// &:active {\n\t\t\t\t// \tbackground-color: #f3f3f3;\n\t\t\t\t// }\n\t\t\t}\n\t\t}\n\t}\n\n\t.DrawerPage {\n\t\tposition: fixed;\n\t\twidth: 100vw;\n\t\theight: 100vh;\n\t\tleft: 0vw;\n\t\tbackground-color: #f1f1f1;\n\t\ttransition: all 0.4s;\n\t}\n\n\t.DrawerPage.show {\n\t\ttransform: scale(0.9, 0.9);\n\t\tleft: 85vw;\n\t\tbox-shadow: 0 0 60upx rgba(0, 0, 0, 0.2);\n\t\ttransform-origin: 0;\n\t}\n\n\t.DrawerWindow {\n\t\tposition: absolute;\n\t\twidth: 85vw;\n\t\theight: 100vh;\n\t\tleft: 0;\n\t\ttop: 0;\n\t\ttransform: scale(0.9, 0.9) translateX(-100%);\n\t\topacity: 0;\n\t\tpointer-events: none;\n\t\ttransition: all 0.4s;\n\t\tpadding: 100upx 0;\n\t}\n\n\t.DrawerWindow.show {\n\t\ttransform: scale(1, 1) translateX(0%);\n\t\topacity: 1;\n\t\tpointer-events: all;\n\t}\n\n\t.DrawerClose {\n\t\tposition: absolute;\n\t\twidth: 40vw;\n\t\theight: 100vh;\n\t\tright: 0;\n\t\ttop: 0;\n\t\tcolor: transparent;\n\t\tpadding-bottom: 30upx;\n\t\tdisplay: flex;\n\t\talign-items: flex-end;\n\t\tjustify-content: center;\n\t\tbackground-image: linear-gradient(90deg, rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.6));\n\t\tletter-spacing: 5px;\n\t\tfont-size: 50upx;\n\t\topacity: 0;\n\t\tpointer-events: none;\n\t\ttransition: all 0.4s;\n\t}\n\n\t.DrawerClose.show {\n\t\topacity: 1;\n\t\tpointer-events: all;\n\t\twidth: 15vw;\n\t\tcolor: #fff;\n\t}\n\n\t.DrawerPage .cu-bar.tabbar .action button.cuIcon {\n\t\twidth: 64upx;\n\t\theight: 64upx;\n\t\tline-height: 64upx;\n\t\tmargin: 0;\n\t\tdisplay: inline-block;\n\t}\n\n\t.DrawerPage .cu-bar.tabbar .action .cu-avatar {\n\t\tmargin: 0;\n\t}\n\n\t.DrawerPage .nav {\n\t\tflex: 1;\n\t}\n\n\t.DrawerPage .nav .cu-item.cur {\n\t\tborder-bottom: 0;\n\t\tposition: relative;\n\t}\n\n\t.DrawerPage .nav .cu-item.cur::after {\n\t\tcontent: \"\";\n\t\twidth: 10upx;\n\t\theight: 10upx;\n\t\tbackground-color: currentColor;\n\t\tposition: absolute;\n\t\tbottom: 10upx;\n\t\tborder-radius: 10upx;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tmargin: auto;\n\t}\n\n\t.DrawerPage .cu-bar.tabbar .action {\n\t\tflex: initial;\n\t}\n\n\n\n\t.page_box {\n\t\t.my_tab {\n\t\t\tz-index: 20;\n\t\t\tbackground-color: #F8F8F8;\n\n\t\t\t.my_tab_title {\n\t\t\t\tz-index: 20;\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 44px; //这个是固定的44px（所有小程序顶部高度都是 = 44px + 手机系统状态栏高度）\n\t\t\t\tline-height: 44px;\n\t\t\t\ttext-align: center;\n\t\t\t\tbackground-color: #F8F8F8;\n\t\t\t\tposition: fixed;\n\t\t\t\ttop: 0;\n\t\t\t\tfont-family: Monospaced Number, Chinese Quote, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, PingFang SC, Hiragino Sans GB, Microsoft YaHei,\n\t\t\t\t\tHelvetica Neue, Helvetica, Arial, sans-serif !important;\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tcolor: #000;\n\t\t\t\tfont-weight: 500;\n\n\t\t\t\t.menu_btn {\n\t\t\t\t\tbackground-color: #ffffff; //这个是小程序默认的标题栏背景色\n\t\t\t\t\toverflow: hidden;\n\n\t\t\t\t\t// position: fixed;//行内式写了固定定位--目的是去掉下划页面一起滚动问题\n\t\t\t\t\t.arrowleft {\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\ttop: 50%;\n\t\t\t\t\t\tleft: 50%;\n\t\t\t\t\t\ttransform: translate(-160%, -50%) !important;\n\t\t\t\t\t\t-webkit-transform: translate(-160%, -50%) !important;\n\t\t\t\t\t}\n\n\t\t\t\t\t.text_box {\n\t\t\t\t\t\twidth: 1rpx;\n\t\t\t\t\t\theight: 20px;\n\t\t\t\t\t\tbackground-color: #ddd;\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\ttop: 50%;\n\t\t\t\t\t\tleft: 50%;\n\t\t\t\t\t\ttransform: translate(-50%, -50%) !important;\n\t\t\t\t\t\t-webkit-transform: translate(-50%, -50%) !important;\n\t\t\t\t\t}\n\n\t\t\t\t\t.home {\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\ttop: 50%;\n\t\t\t\t\t\tleft: 50%;\n\t\t\t\t\t\ttransform: translate(60%, -50%) !important;\n\t\t\t\t\t\t-webkit-transform: translate(60%, -50%) !important;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./aiChat.vue?vue&type=style&index=0&lang=less&scope=true&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./aiChat.vue?vue&type=style&index=0&lang=less&scope=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1737420401401\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}