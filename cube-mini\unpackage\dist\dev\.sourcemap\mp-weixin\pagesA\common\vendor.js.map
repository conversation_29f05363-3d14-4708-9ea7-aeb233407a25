{"version": 3, "sources": ["uni-app:///api/dudu.js"], "names": ["getUUId", "d", "Date", "getTime", "uuid", "replace", "c", "r", "Math", "random", "floor", "toString", "getUserId", "uni", "getStorageSync", "vuex_userid", "chatCompletions", "params", "request", "userPrompt", "conversationId", "fileUrl", "chatType", "chatV2Completions", "getUserChatHistory", "title", "getChatHistoryDetail", "editSessionTitle", "data", "delSession", "upLoadFile", "filePath"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AAIA;AACO,SAASA,OAAO,GAAG;EACzB,IAAIC,CAAC,GAAG,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;EAC5B,IAAIC,IAAI,GAAG,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAASC,CAAC,EAAE;IAC9E,IAAIC,CAAC,GAAG,CAACN,CAAC,GAAGO,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC;IACzCR,CAAC,GAAGO,IAAI,CAACE,KAAK,CAACT,CAAC,GAAG,EAAE,CAAC;IACtB,OAAO,CAACK,CAAC,IAAI,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI,EAAEI,QAAQ,CAAC,EAAE,CAAC;EACrD,CAAC,CAAC;EACF,OAAOP,IAAI;AACZ;AACA;AACA,IAAMQ,SAAS,GAAG,SAAZA,SAAS,GAAS;EACvB,OAAOC,GAAG,CAACC,cAAc,CAAC,cAAc,CAAC,CAACC,WAAW;AACtD,CAAC;;AAED;AACO,SAASC,eAAe,CAACC,MAAM,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACd,KAAK,8CAAuCD,MAAM,CAACE,UAAU,6BAAmBF,MAAM,CAACG,cAAc,qBAAWR,SAAS,EAAE,sBAAYK,MAAM,CAACI,OAAO,uBAAaJ,MAAM,CAACK,QAAQ,CAAE;IACnL,QAAQ,EAAE;EACX,CAAC,CAAC;AACH;;AAEA;AACO,SAASC,iBAAiB,CAACN,MAAM,EAAE;EACzC,OAAO,IAAAC,gBAAO,EAAC;IACd,KAAK,iDAA0CD,MAAM,CAACE,UAAU,6BAAmBF,MAAM,CAACG,cAAc,qBAAWR,SAAS,EAAE,CAAE;IAChI,QAAQ,EAAE;EACX,CAAC,CAAC;AACH;;AAEA;AACO,SAASY,kBAAkB,CAACP,MAAM,EAAE;EAC1C,OAAO,IAAAC,gBAAO,EAAC;IACd,KAAK,4CAAqCN,SAAS,EAAE,oBAAUK,MAAM,CAACQ,KAAK,CAAE;IAC7E,QAAQ,EAAE;EACX,CAAC,CAAC;AACH;;AAGA;AACO,SAASC,oBAAoB,CAACT,MAAM,EAAE;EAC5C,OAAO,IAAAC,gBAAO,EAAC;IACd,KAAK,sDAA+CD,MAAM,CAACG,cAAc,CAAE;IAC3E,QAAQ,EAAE;EACX,CAAC,CAAC;AACH;;AAEA;AACO,SAASO,gBAAgB,CAACC,IAAI,EAAE;EACtC,OAAO,IAAAV,gBAAO,EAAC;IACd,KAAK,EAAE,uBAAuB;IAC9B,QAAQ,EAAE,MAAM;IAChB,MAAM,EAAEU;EACT,CAAC,CAAC;AACH;;AAGA;AACO,SAASC,UAAU,CAACD,IAAI,EAAE;EAChC,OAAO,IAAAV,gBAAO,EAAC;IACd,KAAK,EAAE,6BAA6B;IACpC,QAAQ,EAAE,MAAM;IAChB,MAAM,EAAEU;EACT,CAAC,CAAC;AACH;;AAGA;AACO,SAASE,UAAU,CAACF,IAAI,EAAE;EAChC,OAAO,IAAAV,gBAAO,EAAC;IACd,KAAK,EAAE,gBAAgB;IACvB,QAAQ,EAAE,MAAM;IAChB,UAAU,EAAEU,IAAI,CAACG;EAClB,CAAC,CAAC;AACH,C", "file": "pagesA/common/vendor.js", "sourcesContent": ["import request from '@/utils/request'\nimport {\n\tgetToken\n} from '@/utils/auth'\n\n// 生成UUID\nexport function getUUId() {\n\tvar d = new Date().getTime();\n\tvar uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n\t\tvar r = (d + Math.random() * 16) % 16 | 0;\n\t\td = Math.floor(d / 16);\n\t\treturn (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16);\n\t});\n\treturn uuid;\n}\n// 获取用户Id\nconst getUserId = () => {\n\treturn uni.getStorageSync('storage_data').vuex_userid;\n}\n\n//对话接口\nexport function chatCompletions(params) {\n\treturn request({\n\t\t'url': `/mini/chat/completions?userPrompt=${params.userPrompt}&conversationId=${params.conversationId}&userId=${getUserId()}&fileUrl=${params.fileUrl}&chatType=${params.chatType}`,\n\t\t'method': 'get'\n\t})\n}\n\n//对话接口-非流式\nexport function chatV2Completions(params) {\n\treturn request({\n\t\t'url': `/mini/chat/v2/completions?userPrompt=${params.userPrompt}&conversationId=${params.conversationId}&userId=${getUserId()}`,\n\t\t'method': 'get'\n\t})\n}\n\n//ByUserId查询历史会话列表\nexport function getUserChatHistory(params) {\n\treturn request({\n\t\t'url': `/mini/getUserChatHistory?userId=${getUserId()}&title=${params.title}`,\n\t\t'method': 'get'\n\t})\n}\n\n\n//ByUserId查询历史会话列表\nexport function getChatHistoryDetail(params) {\n\treturn request({\n\t\t'url': `/mini/getChatHistoryDetail?conversationId=${params.conversationId}`,\n\t\t'method': 'get'\n\t})\n}\n\n//编辑会话标题\nexport function editSessionTitle(data) {\n\treturn request({\n\t\t'url': '/mini/updateChatTitle',\n\t\t'method': 'post',\n\t\t'data': data\n\t})\n}\n\n\n//删除会话\nexport function delSession(data) {\n\treturn request({\n\t\t'url': '/mini/deleteUserChatHistory',\n\t\t'method': 'post',\n\t\t'data': data\n\t})\n}\n\n\n//上传文件\nexport function upLoadFile(data) {\n\treturn request({\n\t\t'url': '/common/upload',\n\t\t'method': 'post',\n\t\t'filePath': data.filePath\n\t})\n}"], "sourceRoot": ""}