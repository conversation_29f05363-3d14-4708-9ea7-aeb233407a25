{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/strategy/detail.vue?6c03", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/strategy/detail.vue?28e7", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/strategy/detail.vue?ef12", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/strategy/detail.vue?bc56", "uni-app:///pages/strategy/detail.vue", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/strategy/detail.vue?6ecf", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/strategy/detail.vue?2ad3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "showPopup", "showPopupTwo", "showUrl", "resId", "wxLoginForm", "appId", "appSecret", "code", "encryptedIv", "encryptedData", "nick<PERSON><PERSON>", "avatar", "usercomment", "resInfo", "userId", "commentItems", "browseForm", "shareForm", "comment", "onShareAppMessage", "console", "title", "path", "imageUrl", "onShareTimeline", "created", "withShareTicket", "menus", "onLoad", "computed", "onShow", "uni", "onHide", "methods", "gotoPingLun", "selector", "duration", "onPullDownRefresh", "hidePopup", "closePopupTwo", "backHome", "url", "copyToClipboard", "success", "icon", "fail", "handleConfirm", "toggleCol", "item", "toggleLike", "getResComment", "getStrategyDetail", "onListItemClick", "moreReport", "getRandomColor", "previewDoc", "wx<PERSON><PERSON><PERSON><PERSON><PERSON>", "res", "lang", "desc", "service", "provider", "loginRes", "infoRes", "iv", "sendWxLoginFormToLocalService", "onsole", "loginSuccess"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,mBAAO,CAAC,2CAAkC;AAC1D;AACA;AACA,cAAc,mBAAO,CAAC,4CAAmC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,QAAQ,mBAAO,CAAC,2CAAkC;AAClD;AACA;AACA,MAAM,mBAAO,CAAC,4CAAmC;AACjD;AACA;AACA,8BAA8B,mBAAO,CAAC,wCAA+B;AACrE;AACA,MAAM,mBAAO,CAAC,yCAAgC;AAC9C;AACA,WAAW,mBAAO,CAAC,2CAAkC;AACrD;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjFA;AAAA;AAAA;AAAA;AAAo0B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACoHx1B;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAWA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACA;QACA;QACAC;QACAC;QACA;QACA;QACA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;QACA;QACA;QACA;QACA;QACA;MAAA,CAEA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACA;EACAC;IACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACA;EACAC;IACA;MACAH;MACAC;MACAC;IACA;EACA;EACAE;IACA;IACA/B;MACAgC;MACAC;IACA;IACA;MACAP;IACA;MACA;MACAA;IACA;EACA;EACAQ;IACA;IACA;IACA;EACA;EACAC;EACAC;IACA;IACAC;EACA;EACAC;IACA;IACAD;EACA;EACAE;IACA;IACAC;MACAH;QACAI;QACA;QACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACAjB;MACA;MACAW;IACA;IACAO;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAT;QACAU;MACA;IACA;IACAtB;MAAA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;QACAE;QACAC;MACA;IACA;IAEAoB;MACAX;QACAhC;QACA4C;UACAZ;YACAV;YACAuB;UACA;QACA;QACAC;UACAd;YACAV;YACAuB;UACA;QACA;MACA;IACA;IACAE;MAAA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;UACAf;YACAV;YACAuB;YACAR;UACA;UACA;UACA;UACA;QAEA;MACA;IACA;IACAW;MACA3B;MACA;QACA;QACA4B;QACAA;QACA;MACA;QACA;QACAA;QACAA;QACA;MACA;MACAA;MACA,2DAEA;IACA;IACAC;MACA7B;MACA;QACA;QACA4B;QACAA;QACA;MACA;QACA;QACAA;QACAA;QACA;MACA;MACAA;MACAA;MACA,4DAEA;IACA;IACAE;MAAA;MACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;UACA;QACA;QACA;UACA;QACA;QACA;QACA;QACA;QACA;QACA,oEACA;MACA;IACA;IACAC;MACArB;QACAU;MACA;MACArB;IACA;IACAiC;MACAtB;QACAU;MACA;IACA;IACAa;MACA;MACA,2FACA,qBACA;MACA;MACA;IACA;IACAC;MAAA;MACA7D;QACAiD;UACAvB;UACA;YACA;YACAA;YACA;YACA;YAEA;cACA;YACA;YACAW;cACAU;YACA;UACA;YACA;UACA;QACA;MACA;IACA;IACAe;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA9D;kBACAiD;oBACAvB;oBACA;sBACA1B;wBACAiD;0BACAvB;0BACA;0BACA1B;4BACAiD;8BACAvB,oCACAqC;8BACA;gCACArC;gCACA;gCACA,qCACA;8BACA;gCACAA;8BACA;4BACA;0BACA;wBACA;sBACA;oBACA;sBACAW;wBACA2B;wBACAC;wBACAhB;0BACA;0BACA;0BACAZ;4BACA6B;4BACAjB;8BACA;gCACAZ;kCACA8B;kCACAlB,0BACAmB;oCACA,mBACAvD,OACAuD,SACAvD;oCACAwB;sCACAY,0BACAoB,SACA;wCACA,mBACAvD,cACAuD,QACAC;wCACA,mBACAvD,gBACAsD,QACAtD;wCACA,qCACA,KACA;sCACA;oCACA;kCACA;gCACA;8BACA;4BACA;0BACA;wBACA;wBACAoC,0BAEA;sBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAoB;MAAA;MACA;QACA;UACA;UACA;UACA7C;QACA;UACA8C;QACA;MACA;QACA;UACA;UACA;UACA9C;QACA;UACAA;QACA;MACA;IACA;IACA;IACA+C;MAAA;MACA;MACA;QACA;MACA;IACA;EAGA;AACA;AAAA,2B;;;;;;;;;;;;;ACzfA;AAAA;AAAA;AAAA;AAAmiD,CAAgB,m6CAAG,EAAC,C;;;;;;;;;;;ACAvjD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/strategy/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/strategy/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=1f3f4884&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/strategy/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=1f3f4884&\"", "var components\ntry {\n  components = {\n    uniTag: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-tag/components/uni-tag/uni-tag\" */ \"@/uni_modules/uni-tag/components/uni-tag/uni-tag.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.commentItems.length\n  var l0 =\n    g0 > 0\n      ? _vm.__map(_vm.commentItems, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 =\n            item.isLike === 0\n              ? require(\"@/static/images/icon/dianzan.png\")\n              : null\n          var m1 = !(item.isLike === 0)\n            ? require(\"@/static/images/icon/dianzan1.png\")\n            : null\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n          }\n        })\n      : null\n  var g1 = _vm.commentItems.length\n  var m2 =\n    _vm.resInfo.isLike === 0\n      ? require(\"@/static/images/icon/dianzan.png\")\n      : null\n  var m3 = !(_vm.resInfo.isLike === 0)\n    ? require(\"@/static/images/icon/dianzan1.png\")\n    : null\n  var m4 =\n    _vm.resInfo.isCol === 0 ? require(\"@/static/images/icon/xing.png\") : null\n  var m5 = !(_vm.resInfo.isCol === 0)\n    ? require(\"@/static/images/icon/xing1.png\")\n    : null\n  var m6 = require(\"@/static/images/icon/pinglun.png\")\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"about-container\">\r\n    <view class=\"top\">\r\n      <!-- 标题 -->\r\n      <view class=\"toptitle\">{{ resInfo.strategyTitle }}</view>\r\n      <!-- 标签,作者,发布时间等信息 -->\r\n      <view class=\"info-box\">\r\n        <uni-tag style=\"margin-right: 16rpx;margin-top: 30rpx;\" v-for=\"(item,index) in resInfo.tag\" :key=\"index\"\r\n          :text=\"item\" type=\"primary\" size=\"small\"\r\n          custom-style=\"background-color: #f2f2f2; border-color: #f2f2f2; color: #999999;\" />\r\n        <text class=\"min-text\">{{resInfo.author}}</text>\r\n        <text class=\"min-text\">{{resInfo.createTime}}</text>\r\n      </view>\r\n      <!-- 富文本内容 -->\r\n      <view class=\"content\" v-html=\"resInfo.strategyContent\"></view>\r\n    </view>\r\n\r\n    <!-- 评论区 -->\r\n    <scroll-view scroll-y=\"true\">\r\n      <view class=\"usercomments\">\r\n        <view class=\"comment\" v-if=\"commentItems.length > 0\">\r\n          <view class=\"userLike\">相关评论</view>\r\n          <view class=\"list-item\" v-for=\"(item, index) in commentItems\" :key=\"index\">\r\n            <view class=\"user-info\">\r\n              <view class=\"like-section\">\r\n                <image :src=\"item.avatar\" class=\"avatar\"></image>\r\n                <view style=\"display: flex;flex-direction: column;\">\r\n                  <view class=\"nick-name\">{{ item.nickName }}</view>\r\n                  <view class=\"comment-time\">{{ item.create_time }}</view>\r\n                </view>\r\n              </view>\r\n              <view style=\"display: flex;align-items: center;\">\r\n                <!-- 根据 item.isLike 的值显示不同的 likeIcon -->\r\n                <image v-if=\"item.isLike === 0\" class=\"likeIcon\" :src=\"require('@/static/images/icon/dianzan.png')\"\r\n                  @click=\"toggleLike(item)\"></image>\r\n                <image v-else class=\"likeIcon\" :src=\"require('@/static/images/icon/dianzan1.png')\"\r\n                  @click=\"toggleLike(item)\">\r\n                </image>\r\n                <text style=\"margin-left: 10rpx;\">{{ item.userlike }}</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"comment-content\">{{ item.comment }}</view>\r\n\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"empty\" v-if=\"commentItems.length == 0\">\r\n          暂无评论，抢个沙发吧~\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n\r\n\r\n    <!-- 5. 发送评论框，固定在屏幕底部 -->\r\n    <view class=\"comment-box\">\r\n      <view class=\"search-bar\">\r\n        <input confirm-type=\"done\" v-model=\"usercomment\" type=\"text\" @confirm=\"handleConfirm\" placeholder=\"说点什么~\"\r\n          class=\"uni-input\" />\r\n      </view>\r\n\r\n      <view style=\"display: flex;align-items: center;\">\r\n        <!-- 点赞 -->\r\n        <view class=\"link-box\">\r\n          <image v-if=\"resInfo.isLike === 0\" :src=\"require('@/static/images/icon/dianzan.png')\"\r\n            @click=\"toggleLike(resInfo)\"></image>\r\n          <image v-else :src=\"require('@/static/images/icon/dianzan1.png')\" @click=\"toggleLike(resInfo)\">\r\n          </image>\r\n        </view>\r\n        <!-- 收藏 -->\r\n        <view class=\"link-box\">\r\n          <image v-if=\"resInfo.isCol === 0\" :src=\"require('@/static/images/icon/xing.png')\" @click=\"toggleCol(resInfo)\">\r\n          </image>\r\n          <image v-else :src=\"require('@/static/images/icon/xing1.png')\" @click=\"toggleCol(resInfo)\">\r\n          </image>\r\n        </view>\r\n        <!-- 评论 -->\r\n        <view class=\"link-box\" @click=\"gotoPingLun\">\r\n          <image style=\"width: 46rpx;height: 46rpx;\" :src=\"require('@/static/images/icon/pinglun.png')\"></image>\r\n        </view>\r\n      </view>\r\n\r\n    </view>\r\n\r\n    <!-- 底部弹出窗 -->\r\n    <view class=\"popup-overlay\" v-if=\"showPopup\"></view>\r\n    <view class=\"popup-container\" v-if=\"showPopup\">\r\n      <view class=\"popup-content\">\r\n        <image style=\"width: 200rpx;height: 100rpx;\" src=\"../../../static/images/logo.png\"></image>\r\n        <view class=\"descAuth\">\r\n          <text>\r\n            登录后，您将立即解锁超过 10,000篇精彩研究报告，涵盖各行各业，助您洞察市场动态，把握行业趋势！\r\n          </text>\r\n        </view>\r\n        <button style=\"width: 600rpx;\" type=\"primary\" @click=\"wxhandleLogin\">一键微信授权</button>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"popup-overlay\" v-if=\"showPopupTwo\"></view>\r\n    <view class=\"popup-container\" v-if=\"showPopupTwo\">\r\n      <view class=\"popup-content\">\r\n        <image style=\"width: 200rpx;height: 100rpx;\" src=\"../../../static/images/logo.png\"></image>\r\n        <view class=\"descAuth\">\r\n          <text>\r\n            微信小程序无法直接预览，可通过以下方式获取：\r\n            1.关注【策元】公众号，扫描文章底部群二维码加入分享群获取下载链接。\r\n            2.复制文章顶部原文链接，在浏览器中打开。\r\n          </text>\r\n        </view>\r\n        <button style=\"width: 600rpx;\" type=\"primary\" @click=\"closePopupTwo\">我知道了</button>\r\n      </view>\r\n    </view>\r\n  </view>\r\n\r\n</template>\r\n\r\n<script>\r\n  import storage from '@/utils/storage'\r\n  import constant from '@/utils/constant'\r\n  import {\r\n    getStrategyDetail,\r\n    getResComment,\r\n    changeCommentStatus,\r\n    changeResColStatus,\r\n    saveUserComment,\r\n    saveUserBrowse,\r\n    saveUserDown,\r\n    saveUserShare\r\n  } from '@/api/report'\r\n\r\n  export default {\r\n    data() {\r\n      return {\r\n        showPopup: false,\r\n        showPopupTwo: false,\r\n        showUrl: \"\",\r\n        resId: '',\r\n        wxLoginForm: {\r\n          //上线切换\r\n          //策元\r\n          appId: \"wx9812ae7b613f2e49\",\r\n          appSecret: \"f9be6c7aa2bf0ea7d0d3edfe7abd7280\",\r\n          //群快讯\r\n          // appId: \"wx472c5804bcfd4984\",\r\n          // appSecret: \"8de4ae12f2944f95cd85d479709e90ae\",\r\n          code: \"\",\r\n          encryptedIv: \"\",\r\n          encryptedData: \"\",\r\n          nickName: \"\",\r\n          avatar: \"\"\r\n        },\r\n        usercomment: '',\r\n        resInfo: {\r\n          // title: '干货标题',\r\n          // res_url: '干货链接',\r\n          // create_time: '创建时间',\r\n          // resource: '干货来源',\r\n          // browse_num: '干货浏览人数',\r\n\r\n        },\r\n        userId: storage.get(constant.userId),\r\n        commentItems: [],\r\n        browseForm: {},\r\n        shareForm: {},\r\n        comment: {}\r\n      }\r\n    },\r\n    // 分享到微信好友\r\n    onShareAppMessage() {\r\n      console.log(\"分享好友\")\r\n      return {\r\n        title: '干货详情',\r\n        path: '/pages/user/detail/index?id=' + this.resId,\r\n        imageUrl: '',\r\n      }\r\n    },\r\n    // 分享到朋友圈\r\n    onShareTimeline() {\r\n      return {\r\n        title: '攻略详情',\r\n        path: '/pages/user/detail/index?id=' + this.resId,\r\n        imageUrl: '',\r\n      }\r\n    },\r\n    created() {\r\n      this.$modal.loading(\"加载中，请耐心等待...\")\r\n      wx.showShareMenu({\r\n        withShareTicket: true,\r\n        menus: ['shareAppMessage', 'shareTimeline']\r\n      });\r\n      if (this.userId) {\r\n        console.log(\"已经登录\")\r\n      } else {\r\n        this.showPopup = true; // 显示弹出窗\r\n        console.log(\"没有登录\")\r\n      }\r\n    },\r\n    onLoad(options) {\r\n      this.resId = options.id\r\n      this.getStrategyDetail(options.id);\r\n      this.getResComment(this.resId)\r\n    },\r\n    computed: {},\r\n    onShow() {\r\n      // 监听下拉刷新事件\r\n      uni.startPullDownRefresh();\r\n    },\r\n    onHide() {\r\n      // 停止监听下拉刷新事件\r\n      uni.stopPullDownRefresh();\r\n    },\r\n    methods: {\r\n      // 跳转到评论的位置\r\n      gotoPingLun() {\r\n        uni.pageScrollTo({\r\n          selector: \".usercomments\",\r\n          // scrollTop: rect.top,\r\n          duration: 2000\r\n        })\r\n        // console.log(uni.createSelectorQuery().select('.usercomments').boundingClientRect());\r\n        // uni.createSelectorQuery().select('.usercomments').boundingClientRect((rect) => {\r\n        //   console.log(1111);\r\n        //   console.log(rect);\r\n        //   if (rect) {\r\n        //     uni.pageScrollTo({\r\n        //       scrollTop: rect.top,\r\n        //       duration: 3000\r\n        //     })\r\n        //   }\r\n        // })\r\n      },\r\n      onPullDownRefresh() {\r\n        this.getStrategyDetail(this.resId);\r\n        this.getResComment(this.resId)\r\n        // 在这里处理下拉刷新事件\r\n        console.log('下拉刷新触发');\r\n        // 停止下拉刷新动画\r\n        uni.stopPullDownRefresh();\r\n      },\r\n      hidePopup() {\r\n        this.showPopup = false;\r\n      },\r\n      closePopupTwo() {\r\n        this.showPopupTwo = false;\r\n      },\r\n      backHome() {\r\n        uni.switchTab({\r\n          url: '/pages/index'\r\n        });\r\n      },\r\n      onShareAppMessage(res) {\r\n        this.shareForm.userId = this.userId\r\n        this.shareForm.bizId = this.resId\r\n        this.shareForm.path = '干货详情'\r\n        saveUserShare(this.shareForm).then(res => {\r\n          this.$modal.showToast(res.data)\r\n        })\r\n        return {\r\n          title: '攻略分享',\r\n          path: `/pages/strategy/detail?id=` + this.resId\r\n        }\r\n      },\r\n\r\n      copyToClipboard() {\r\n        uni.setClipboardData({\r\n          data: this.resInfo.res_url,\r\n          success: function() {\r\n            uni.showToast({\r\n              title: '复制成功',\r\n              icon: 'success'\r\n            });\r\n          },\r\n          fail: function() {\r\n            uni.showToast({\r\n              title: '复制失败',\r\n              icon: 'none'\r\n            });\r\n          }\r\n        });\r\n      },\r\n      handleConfirm(e) {\r\n        this.$modal.loading(\"发送评论中，请耐心等待...\")\r\n        this.comment.comment = this.usercomment;\r\n        this.comment.userId = this.userId;\r\n        this.comment.resId = this.resId;\r\n        if (this.usercomment != '') {\r\n          saveUserComment(this.comment).then(res => {\r\n            this.$modal.closeLoading()\r\n            uni.showToast({\r\n              title: '评论成功',\r\n              icon: \"success\",\r\n              duration: 1500\r\n            })\r\n            // this.$modal.showToast('您的评论正在审核中，审核通过后会显示在评论区哦~')\r\n            this.usercomment = ''\r\n            this.getResComment(this.resId)\r\n\r\n          })\r\n        }\r\n      },\r\n      toggleCol(item) {\r\n        console.log(item)\r\n        if (item.isCol === 1) {\r\n          // 如果 isCol 为 1，则 col 减 1，并且 isCol 变为 0\r\n          item.collectionNum -= 1;\r\n          item.isCol = 0;\r\n          this.$modal.showToast('已取消收藏')\r\n        } else if (item.isCol === 0) {\r\n          // 如果 isCol 大于 1，则 col 加 1，并且 isCol 变为 1\r\n          item.collectionNum += 1;\r\n          item.isCol = 1;\r\n          this.$modal.showToast('已成功收藏')\r\n        }\r\n        item.userId = this.userId;\r\n        changeResColStatus(item).then(res => {\r\n\r\n        })\r\n      },\r\n      toggleLike(item) {\r\n        console.log(item)\r\n        if (item.isLike === 1) {\r\n          // 如果 isLike 为 1，则 like 减 1，并且 isLike 变为 0\r\n          item.userlike -= 1;\r\n          item.isLike = 0;\r\n          this.$modal.showToast('已取消点赞')\r\n        } else if (item.isLike === 0) {\r\n          // 如果 isLike 大于 1，则 like 加 1，并且 isLike 变为 1\r\n          item.userlike += 1;\r\n          item.isLike = 1;\r\n          this.$modal.showToast('已成功点赞')\r\n        }\r\n        item.creator = this.userId;\r\n        item.comId = item.id\r\n        changeCommentStatus(item).then(res => {\r\n\r\n        })\r\n      },\r\n      getResComment(resId) {\r\n        getResComment(resId).then(res => {\r\n          this.commentItems = res.data\r\n        })\r\n      },\r\n      // 获取详情\r\n      getStrategyDetail(id) {\r\n        getStrategyDetail(id).then(res => {\r\n          this.resInfo = res.data\r\n          if (this.resInfo.tag) {\r\n            this.resInfo.tag = this.resInfo.tag.split(',')\r\n          }\r\n          if (this.resInfo.createTime) {\r\n            this.resInfo.createTime = this.resInfo.createTime.replace('T', ' ')\r\n          }\r\n          this.browseForm.userId = this.userId;\r\n          this.browseForm.id = this.resId;\r\n          // 阅读记录\r\n          this.$modal.closeLoading()\r\n          saveUserBrowse(this.browseForm).then(res => {\r\n          })\r\n        })\r\n      },\r\n      onListItemClick(item) {\r\n        uni.navigateTo({\r\n          url: '/pages/user/detail/index?id=' + item.id\r\n        });\r\n        console.log('点击了列表项', item);\r\n      },\r\n      moreReport() {\r\n        uni.switchTab({\r\n          url: '/pages/index'\r\n        });\r\n      },\r\n      getRandomColor() {\r\n        // 预定义的颜色集合\r\n        const colors = ['#87CEEB', '#66CDAA', '#CD5C5C', '#DDA0DD', '#FFDAB9', '#C1CDC1', '#D1EEEE',\r\n          '#EED2EE', '#9F79EE'\r\n        ];\r\n        // 从集合中随机选择一个颜色\r\n        return colors[Math.floor(Math.random() * colors.length)];\r\n      },\r\n      previewDoc() {\r\n        wx.getSystemInfo({\r\n          success: (res) => {\r\n            console.log('res:', res)\r\n            if (res.environment) {\r\n              this.$modal.loading(\"加载中，请耐心等待...\")\r\n              console.log(this.resInfo.res_url)\r\n              this.browseForm.userId = this.userId;\r\n              this.browseForm.id = this.resId;\r\n\r\n              saveUserDown(this.browseForm).then(res => {\r\n                this.$modal.closeLoading()\r\n              })\r\n              uni.navigateTo({\r\n                url: '/pages/user/detail/pdfview?url=' + encodeURIComponent(this.resInfo.res_url)\r\n              });\r\n            } else {\r\n              this.showPopupTwo = true;\r\n            }\r\n          }\r\n        })\r\n      },\r\n      async wxhandleLogin() {\r\n        this.$modal.loading(\"登录中，请耐心等待...\")\r\n        wx.getSystemInfo({\r\n          success: (res) => {\r\n            console.log('res:', res)\r\n            if (res.environment) {\r\n              wx.login({\r\n                success: (res) => {\r\n                  console.log(\"微信code\" + res.code)\r\n                  this.wxLoginForm.code = res.code\r\n                  wx.qy.login({\r\n                    success: (res) => {\r\n                      console.log(\"企业微信\" + JSON.stringify(\r\n                        res))\r\n                      if (res.code) {\r\n                        console.log(\"企业微信code=\" + res.code)\r\n                        this.wxLoginForm.qwcode = res.code\r\n                        this.sendWxLoginFormToLocalService(\r\n                          'qywx')\r\n                      } else {\r\n                        console.log('登录失败！' + res.errMsg)\r\n                      }\r\n                    }\r\n                  });\r\n                }\r\n              });\r\n            } else {\r\n              uni.getUserProfile({\r\n                lang: 'zh_CN',\r\n                desc: '用于完善会员资料',\r\n                success: (user) => {\r\n                  this.wxLoginForm.nickName = user.userInfo.nickName\r\n                  this.wxLoginForm.avatar = user.userInfo.avatarUrl\r\n                  uni.getProvider({\r\n                    service: 'oauth',\r\n                    success: (res) => {\r\n                      if (~res.provider.indexOf(\"weixin\")) {\r\n                        uni.login({\r\n                          provider: \"weixin\",\r\n                          success: (\r\n                            loginRes) => {\r\n                            this.wxLoginForm\r\n                              .code =\r\n                              loginRes\r\n                              .code\r\n                            uni.getUserInfo({\r\n                              success: (\r\n                                infoRes\r\n                              ) => {\r\n                                this.wxLoginForm\r\n                                  .encryptedIv =\r\n                                  infoRes\r\n                                  .iv\r\n                                this.wxLoginForm\r\n                                  .encryptedData =\r\n                                  infoRes\r\n                                  .encryptedData\r\n                                this.sendWxLoginFormToLocalService(\r\n                                  'wx'\r\n                                )\r\n                              }\r\n                            })\r\n                          }\r\n                        })\r\n                      }\r\n                    }\r\n                  })\r\n                },\r\n                fail(res) {\r\n\r\n                }\r\n              })\r\n            }\r\n          }\r\n        })\r\n      },\r\n      sendWxLoginFormToLocalService(env) {\r\n        if (env == 'wx') {\r\n          this.$store.dispatch('WxLogin', this.wxLoginForm).then(() => {\r\n            this.$modal.closeLoading()\r\n            this.loginSuccess()\r\n            console.log('登录成功')\r\n          }).catch(() => {\r\n            onsole.log('登录失败')\r\n          })\r\n        } else if (env == 'qywx') {\r\n          this.$store.dispatch('QyWxLogin', this.wxLoginForm).then(() => {\r\n            this.$modal.closeLoading()\r\n            this.loginSuccess()\r\n            console.log('企业微信登录成功')\r\n          }).catch(() => {\r\n            console.log('企业微信登录失败')\r\n          })\r\n        }\r\n      },\r\n      // 登录成功后，处理函数\r\n      loginSuccess(result) {\r\n        // 设置用户信息\r\n        this.$store.dispatch('GetInfo').then(res => {\r\n          this.$tab.reLaunch('/pages/user/detail/index?id=' + this.resId)\r\n        })\r\n      },\r\n\r\n\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n  .about-container {\r\n    box-sizing: border-box;\r\n    padding: 10rpx 36rpx 30rpx;\r\n  }\r\n\r\n  .toptitle {\r\n    font-size: 40rpx;\r\n    font-weight: bold;\r\n    // margin-bottom: 30rpx;\r\n  }\r\n\r\n  .info-box {\r\n    display: flex;\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .min-text {\r\n    font-size: 26rpx;\r\n    color: #999999;\r\n    margin-right: 16rpx;\r\n    margin-top: 30rpx;\r\n  }\r\n\r\n  .content {\r\n    margin-top: 50rpx;\r\n  }\r\n\r\n  .preview {\r\n    background-color: white;\r\n    padding-left: 40rpx;\r\n    padding-top: 40rpx;\r\n    padding-bottom: 40rpx;\r\n  }\r\n\r\n  .comments {\r\n    width: 100%;\r\n    border-top: 1px solid #F5F5F5;\r\n    margin-top: 30rpx;\r\n  }\r\n\r\n  .usercomments {\r\n    margin-top: 30rpx;\r\n    width: 100%;\r\n    border-top: 1px solid #F5F5F5;\r\n    padding-bottom: 135rpx;\r\n    /* 添加底部内边距，确保最后一条评论不会被遮挡 */\r\n  }\r\n\r\n  .comment {\r\n    background-color: white;\r\n  }\r\n\r\n  .comment .list-item {\r\n    margin-top: 20rpx;\r\n  }\r\n\r\n  .userLike {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    margin: 26rpx 0;\r\n  }\r\n\r\n  .userLike text {\r\n    color: #8f8f94;\r\n    text-align: center;\r\n  }\r\n\r\n  .empty {\r\n    box-sizing: border-box;\r\n    padding: 30rpx;\r\n    text-align: center;\r\n    color: #999;\r\n  }\r\n\r\n\r\n  .list-item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    background-color: white;\r\n    border-bottom: 1px solid #F5F5F5;\r\n  }\r\n\r\n\r\n  .point-text {\r\n    // padding: 30rpx;\r\n    margin-left: 15rpx;\r\n  }\r\n\r\n  .tags {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    margin-left: 20rpx;\r\n  }\r\n\r\n  .tag {\r\n    padding: 4px 8px;\r\n    margin: 4px;\r\n    border-radius: 4px;\r\n    color: white;\r\n    font-size: 8px;\r\n  }\r\n\r\n\r\n\r\n  .likeIcon {\r\n    width: 30rpx;\r\n    height: 30rpx;\r\n    // margin-left: 400rpx;\r\n  }\r\n\r\n  .bottomIcon {\r\n    width: 40rpx;\r\n    height: 40rpx;\r\n    margin-left: 10rpx;\r\n  }\r\n\r\n  .downIcon {\r\n    width: 40rpx;\r\n    height: 40rpx;\r\n    margin-right: 95rpx;\r\n    margin-top: 6rpx;\r\n  }\r\n\r\n  .indexIcon {\r\n    width: 40rpx;\r\n    height: 40rpx;\r\n    margin-top: 5rpx;\r\n    margin-right: 10rpx;\r\n  }\r\n\r\n  .user-info {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .avatar {\r\n    width: 64rpx;\r\n    height: 64rpx;\r\n    border-radius: 50%;\r\n    margin-right: 16rpx;\r\n    vertical-align: middle;\r\n  }\r\n\r\n  .nick-name {\r\n    vertical-align: middle;\r\n    font-size: 28rpx;\r\n    color: #1E1E1E;\r\n  }\r\n\r\n  .like-section {\r\n    flex: 1;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n    margin-right: 20rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 20rpx;\r\n  }\r\n\r\n  .like-count {\r\n    // margin-left: 30vh;\r\n  }\r\n\r\n  .comment-content {\r\n    font-size: 28rpx;\r\n    color: #1E1E1E;\r\n    margin-bottom: 20rpx;\r\n  }\r\n\r\n  .comment-time {\r\n    font-size: 24rpx;\r\n    color: #999999;\r\n  }\r\n\r\n  .comment-box {\r\n    position: fixed;\r\n    bottom: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 120rpx;\r\n    z-index: 1;\r\n    background-color: white;\r\n    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    box-sizing: border-box;\r\n    padding: 0 26rpx;\r\n  }\r\n\r\n  .search-bar {\r\n    flex: 1;\r\n    margin-right: 36rpx;\r\n  }\r\n\r\n  .search-bar input {\r\n    background-color: #f5f5f5;\r\n    height: 80rpx;\r\n    border-radius: 50rpx;\r\n    padding: 0 20rpx;\r\n  }\r\n\r\n\r\n  .resUrl {\r\n    display: inline-block;\r\n    max-width: 70%;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n    color: black;\r\n  }\r\n\r\n  .title {\r\n    display: inline-block;\r\n    max-width: 90%;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n  }\r\n\r\n  .shareBtn {\r\n    margin-top: -10rpx;\r\n    background-color: white;\r\n  }\r\n\r\n  .shareBtn::after {\r\n\r\n    border: none;\r\n  }\r\n\r\n  /* ...其他样式... */\r\n\r\n  .popup-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: rgba(0, 0, 0, 0.5);\r\n    z-index: 999;\r\n  }\r\n\r\n  .popup-container {\r\n    position: fixed;\r\n    bottom: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    background: white;\r\n    z-index: 1000;\r\n    transition: transform 0.3s ease-in-out;\r\n  }\r\n\r\n  .popup-content image {\r\n    margin-top: 100rpx;\r\n    margin-bottom: 50rpx;\r\n    margin-left: 280rpx;\r\n  }\r\n\r\n  .popup-content .descAuth {\r\n    padding: 50rpx;\r\n  }\r\n\r\n  .popup-content button {\r\n    margin-top: 20rpx;\r\n    margin-bottom: 50rpx;\r\n    border-radius: 50rpx;\r\n  }\r\n\r\n  .link-box image {\r\n    width: 40rpx;\r\n    height: 40rpx;\r\n    margin-right: 20rpx;\r\n  }\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1737420405800\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}