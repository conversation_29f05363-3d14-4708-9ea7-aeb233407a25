{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/user/detail/index.vue?efcb", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/user/detail/index.vue?4337", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/user/detail/index.vue?a372", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/user/detail/index.vue?0320", "uni-app:///pages/user/detail/index.vue", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/user/detail/index.vue?547e", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/user/detail/index.vue?5be2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "showPopup", "showPopupTwo", "showUrl", "resId", "wxLoginForm", "appId", "appSecret", "code", "encryptedIv", "encryptedData", "nick<PERSON><PERSON>", "avatar", "usercomment", "resInfo", "title", "res_url", "create_time", "resource", "browse_num", "userId", "listItems", "commentItems", "browseForm", "shareForm", "comment", "onShareAppMessage", "console", "path", "imageUrl", "onShareTimeline", "created", "withShareTicket", "menus", "onLoad", "computed", "onShow", "uni", "onHide", "methods", "onPullDownRefresh", "hidePopup", "closePopupTwo", "backHome", "url", "copyToClipboard", "success", "icon", "fail", "handleConfirm", "toggleLike", "item", "getResComment", "getReporttDeail", "onListItemClick", "moreReport", "getUserLike", "text", "color", "getRandomColor", "previewDoc", "wx<PERSON><PERSON><PERSON><PERSON><PERSON>", "res", "lang", "desc", "service", "provider", "loginRes", "infoRes", "iv", "sendWxLoginFormToLocalService", "onsole", "loginSuccess"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmN;AACnN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA,WAAW,mBAAO,CAAC,wCAA+B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,mBAAO,CAAC,0CAAiC;AACzD;AACA;AACA,cAAc,mBAAO,CAAC,wCAA+B;AACrD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAk1B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACmIt2B;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAQA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACA;QACA;QACAC;QACAC;QACA;QACA;QACA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MAEA;MACAC;MACAC;MACAC,gBAEA;MACAC;MACAC;MACAC;IACA;EACA;EACA;EACAC;IACAC;IACA;MACAZ;MACAa;MACAC;IACA;EACA;EACA;EACAC;IACA;MACAf;MACAa;MACAC;IACA;EACA;EACAE;IACA;IACA;IACApC;MACAqC;MACAC;IACA;IACA;MAEAN;IACA;MACA;MACAA;IACA;EACA;EACAO;IAEA;IACA;IACA;EACA;EACAC,WAEA;EACAC;IACA;IACAC;EACA;EACAC;IACA;IACAD;EACA;EACAE;IACAC;MACA;MACA;MACA;MACAb;;MAIA;MACAU;IACA;IACAI;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAN;QACAO;MACA;IACA;IACAlB;MAAA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;QACAX;QACAa;MACA;IACA;IAEAiB;MAAA;MAEAR;QACArC;QACA8C;UACAT;YACAtB;YACAgC;UACA;QAEA;QACAC;UACAX;YACAtB;YACAgC;UACA;QACA;MACA;MACA;MACA;MAGA;QACA;MACA;IACA;IACAE;MAAA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;UACA;UACA;UACA;QAEA;MACA;IACA;IACAC;MACAvB;MACA;QACA;QACAwB;QACAA;QACA;MACA;QACA;QACAA;QACAA;QACA;MACA;MACAA;MACA,4DAEA;IAEA;IACAC;MAAA;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;QACA,oEAEA;MACA;IACA;IACAC;MACAjB;QACAO;MACA;MACAjB;IACA;IACA4B;MACAlB;QACAO;MACA;IACA;IACAY;MAAA;MACA;QACA;QACA;UACAL;YAAA;cACAM;cACAC;YACA;UAAA;QACA;MACA;IACA;IACAC;MACA;MACA,2FACA,qBACA;MACA;MACA;IACA;IACAC;MAAA;MACAjE;QACAmD;UACAnB;UACA;YACA;YAEAA;YACA;YACA;YAEA;cACA;YACA;YACAU;cACAO;YACA;UAEA;YACA;UACA;QACA;MACA;IAIA;IACAiB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAGA;gBACAlE;kBACAmD;oBACAnB;oBACA;sBAEAhC;wBACAmD;0BACAnB;0BAEA;0BACAhC;4BACAmD;8BACAnB,oCACAmC;8BACA;gCACAnC;gCACA;gCACA,qCACA;8BACA;gCACAA;8BACA;4BACA;0BACA;wBAEA;sBACA;oBAIA;sBACAU;wBACA0B;wBACAC;wBACAlB;0BACA;0BACA;0BACAT;4BACA4B;4BACAnB;8BACA;gCACAT;kCACA6B;kCACApB,0BACAqB;oCACA,mBACA3D,OACA2D,SACA3D;oCACA6B;sCACAS,0BACAsB,SACA;wCACA,mBACA3D,cACA2D,QACAC;wCACA,mBACA3D,gBACA0D,QACA1D;wCACA,qCACA,KACA;sCACA;oCACA;kCACA;gCACA;8BACA;4BACA;0BACA;wBACA;wBACAsC,0BAEA;sBACA;oBAEA;kBAEA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IACAsB;MAAA;MACA;QACA;UACA;UACA;UACA3C;QACA;UACA4C;QACA;MACA;QACA;UACA;UACA;UACA5C;QACA;UACAA;QACA;MACA;IAEA;IACA;IACA6C;MAAA;MACA;MACA;QACA;MACA;IACA;EAGA;AACA;AAAA,2B;;;;;;;;;;;;;ACtgBA;AAAA;AAAA;AAAA;AAA6jD,CAAgB,k6CAAG,EAAC,C;;;;;;;;;;;ACAjlD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/detail/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/user/detail/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=9715f3a2&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/detail/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=9715f3a2&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = require(\"@/static/images/icon/copy.png\")\n  var g0 = _vm.commentItems.length\n  var g1 = _vm.commentItems.length\n  var g2 = _vm.commentItems.length\n  var l0 =\n    g2 > 0\n      ? _vm.__map(_vm.commentItems, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m1 =\n            item.isLike === 0\n              ? require(\"@/static/images/icon/noLike.png\")\n              : null\n          var m2 = !(item.isLike === 0)\n            ? require(\"@/static/images/icon/like.png\")\n            : null\n          return {\n            $orig: $orig,\n            m1: m1,\n            m2: m2,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"about-container\">\r\n\t\t<!-- 1. 顶部显示加大字体的标题 -->\r\n\t\t<view class=\"top\">\r\n\t\t\t<view class=\"toptitle\">{{ resInfo.title }}</view>\r\n\t\t\t<!-- 2. 标题下方展示研报创建时间、研报来源和浏览人数 -->\r\n\t\t\t<view class=\"info-container\">\r\n\t\t\t\t<view style=\"margin-top: 0.1vh;\">{{ resInfo.create_time }}</view>\r\n\t\t\t\t<view style=\"margin-top: -0.2vh;\">{{ resInfo.resource }}</view>\r\n\t\t\t\t<view style=\"margin-top: -0.2vh;\">{{ resInfo.browse_num }}人看过</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 3. 预览区域 -->\r\n\t\t\t<view class=\"preview\">\r\n\t\t\t\t<!-- 显示PDF预览，根据 resInfo.res_url 进行展示 -->\r\n\t\t\t\t<text class=\"resUrl\" style=\"color: black;font-weight: bold;\">干货链接：{{resInfo.res_url}}</text>\r\n\t\t\t\t<image  class=\"bottomIcon\" :src=\"require('@/static/images/icon/copy.png')\" @click=\"copyToClipboard\">\r\n\t\t\t\t</image>\r\n\t\t\t\t<text class=\"resUrl\" style=\"color: black;font-weight: bold;padding-left: 10rpx;\" @click=\"copyToClipboard\">&nbsp;&nbsp;复制链接</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\r\n\r\n\r\n\t\t<view class=\"comments\">\r\n\t\t\t<!-- 评论列表，每条评论包括评论人名称，评论时间和评论内容 -->\r\n\t\t\t<!-- 样式请根据需求自行美化 -->\r\n\t\t\t<view class=\"userLike\">\r\n\t\t\t\t<text class=\"text\" style=\"color: black;font-weight: bold;\"> 猜你喜欢</text>\r\n\t\t\t\t<text class=\"text\" style=\"color: black;font-weight: bold;\" @click=\"moreReport\"> 更多推荐>></text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"list-item\" v-for=\"(item, index) in listItems\" :key=\"index\" @click=\"onListItemClick(item)\">\r\n\t\t\t\t<view class=\"point-text\">\r\n\r\n\t\t\t\t\t<view class=\"title\">{{index+1}}.{{ item.title }}</view>\r\n\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 相关评论 -->\r\n\t\t<view class=\"usercomments\">\r\n\t\t\t<view class=\"userLike\" v-if=\"commentItems.length > 0\">\r\n\t\t\t\t<text class=\"text\" style=\"color: black;font-weight: bold;\">相关评论</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"userLike\" v-if=\"commentItems.length == 0\" style=\"height: 520rpx;\">\r\n\t\t\t\t<text class=\"text\">暂无评论，抢个沙发吧~</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"comment\" v-if=\"commentItems.length > 0\">\r\n\t\t\t\t<view class=\"list-item\" v-for=\"(item, index) in commentItems\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"user-info\">\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"like-section\">\r\n\t\t\t\t\t\t\t<image :src=\"item.avatar\" class=\"avatar\"></image>\r\n\t\t\t\t\t\t\t<text class=\"nick-name\">{{ item.nickName }}</text>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<!-- 根据 item.isLike 的值显示不同的 likeIcon -->\r\n\t\t\t\t\t\t\t<image v-if=\"item.isLike === 0\" class=\"likeIcon\"\r\n\t\t\t\t\t\t\t\t:src=\"require('@/static/images/icon/noLike.png')\" @click=\"toggleLike(item)\"></image>\r\n\t\t\t\t\t\t\t<image v-else class=\"likeIcon\" :src=\"require('@/static/images/icon/like.png')\"\r\n\t\t\t\t\t\t\t\t@click=\"toggleLike(item)\"></image>\r\n\t\t\t\t\t\t\t<text>{{ item.userlike }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"comment-content\">{{ item.comment }}</view>\r\n\t\t\t\t\t<view class=\"comment-time\">{{ item.create_time }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\r\n\t\t<!-- 5. 发送评论框，固定在屏幕底部 -->\r\n\t\t<view class=\"comment-box\">\r\n\t\t\t<view class=\"search-bar\">\r\n\t\t\t\t<input v-model=\"usercomment\" type=\"text\" @confirm=\"handleConfirm\" placeholder=\"说点什么~\" />\r\n\t\t\t<!-- \t<button open-type=\"share\" class=\"shareBtn\">\r\n\t\t\t\t\t<image style=\"width: 40rpx;height: 40rpx\" :src=\"require('@/static/images/icon/sharewx.png')\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t</button> -->\r\n\t          <!--  <button  class=\"shareBtn\">\r\n\t\t\t\t\t<image style=\"width: 40rpx;height: 40rpx\" :src=\"require('@/static/images/icon/sharewx.png')\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t</button> -->\r\n\t\t\t\t<!-- <image class=\"bottomIcon\"  :src=\"require('@/static/images/icon/share.png')\"></image> -->\r\n<!-- \t\t\t\t<image class=\"downIcon\" :src=\"require('@/static/images/icon/jump.png')\" @click=\"previewDoc\"></image>\r\n\r\n\t\t\t\t<image class=\"indexIcon\" :src=\"require('@/static/images/icon/index.png')\" @click=\"backHome\"></image> -->\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\r\n\r\n\t\t<!-- 底部弹出窗 -->\r\n\t\t<view class=\"popup-overlay\" v-if=\"showPopup\"></view>\r\n\t\t<view class=\"popup-container\" v-if=\"showPopup\">\r\n\t\t\t<view class=\"popup-content\">\r\n\t\t\t\t<image style=\"width: 200rpx;height: 100rpx;\" src=\"../../../static/images/logo.png\"></image>\r\n\t\t\t\t<view class=\"descAuth\">\r\n\t\t\t\t\t<text>\r\n\t\t\t\t\t\t登录后，您将立即解锁超过 10,000篇精彩研究报告，涵盖各行各业，助您洞察市场动态，把握行业趋势！\r\n\t\t\t\t\t</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<button style=\"width: 600rpx;\" type=\"primary\" @click=\"wxhandleLogin\">一键微信授权</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t\t<view class=\"popup-overlay\" v-if=\"showPopupTwo\"></view>\r\n\t\t<view class=\"popup-container\" v-if=\"showPopupTwo\">\r\n\t\t\t<view class=\"popup-content\">\r\n\t\t\t\t<image style=\"width: 200rpx;height: 100rpx;\" src=\"../../../static/images/logo.png\"></image>\r\n\t\t\t\t<view class=\"descAuth\">\r\n\t\t\t\t\t<text>\r\n\t\t\t\t\t\t微信小程序无法直接预览，可通过以下方式获取：\r\n\t\t\t\t\t\t1.关注【策元】公众号，扫描文章底部群二维码加入分享群获取下载链接。\r\n\t\t\t\t\t\t2.复制文章顶部原文链接，在浏览器中打开。\r\n\t\t\t\t\t</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button style=\"width: 600rpx;\" type=\"primary\" @click=\"closePopupTwo\">我知道了</button>\r\n\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t</view>\r\n\r\n</template>\r\n\r\n<script>\r\n\timport storage from '@/utils/storage'\r\n\timport constant from '@/utils/constant'\r\n\timport {\r\n\t\tgetReporttDeail,\r\n\t\tgetUserLike,\r\n\t\tgetResComment,\r\n\t\tchangeCommentStatus,\r\n\t\tsaveUserComment, saveUserBrowse,saveUserDown,saveUserShare\r\n\t} from '@/api/report'\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshowPopup: false,\r\n\t\t\t\tshowPopupTwo: false,\r\n\t\t\t\tshowUrl: \"\",\r\n\t\t\t\tresId: '',\r\n\t\t\t\twxLoginForm: {\r\n\t\t\t\t\t//上线切换\r\n\t\t\t\t\t//策元\r\n\t\t\t\t\tappId:\"wx9812ae7b613f2e49\",\r\n\t\t\t\t\tappSecret: \"f9be6c7aa2bf0ea7d0d3edfe7abd7280\",\r\n\t\t\t\t\t//群快讯\r\n\t\t\t\t\t// appId: \"wx472c5804bcfd4984\",\r\n\t\t\t\t\t// appSecret: \"8de4ae12f2944f95cd85d479709e90ae\",\r\n\t\t\t\t\tcode: \"\",\r\n\t\t\t\t\tencryptedIv: \"\",\r\n\t\t\t\t\tencryptedData: \"\",\r\n\t\t\t\t\tnickName: \"\",\r\n\t\t\t\t\tavatar: \"\"\r\n\t\t\t\t},\r\n\t\t\t\tusercomment: '',\r\n\t\t\t\tresInfo: {\r\n\t\t\t\t\ttitle: '干货标题',\r\n\t\t\t\t\tres_url: '干货链接',\r\n\t\t\t\t\tcreate_time: '创建时间',\r\n\t\t\t\t\tresource: '干货来源',\r\n\t\t\t\t\tbrowse_num: '干货浏览人数',\r\n\r\n\t\t\t\t},\r\n\t\t\t\tuserId: storage.get(constant.userId),\r\n\t\t\t\tlistItems: [],\r\n\t\t\t\tcommentItems: [\r\n\r\n\t\t\t\t],\r\n\t\t\t\tbrowseForm:{},\r\n\t\t\t\tshareForm:{},\r\n\t\t\t\tcomment: {}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 分享到微信好友\r\n\t\tonShareAppMessage() {\r\n\t\t  console.log(\"分享好友\")\r\n\t\t\treturn {\r\n\t\t\t\ttitle: '干货详情',\r\n\t\t\t\tpath: '/pages/user/detail/index?id=' + this.resId,\r\n\t\t\t\timageUrl: '',\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 分享到朋友圈\r\n\t\tonShareTimeline() {\r\n\t\t\treturn {\r\n\t\t\t\ttitle: '干货详情',\r\n\t\t\t\tpath: '/pages/user/detail/index?id=' + this.resId,\r\n\t\t\t\timageUrl: '',\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.$modal.loading(\"加载中，请耐心等待...\")\r\n\t\t\tthis.getUserLike(this.userId);\r\n\t\t\twx.showShareMenu({\r\n\t\t\t\twithShareTicket: true,\r\n\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\r\n\t\t\t});\r\n\t\t\tif (this.userId) {\r\n\t\t\t\t\r\n\t\t\t\tconsole.log(\"已经登录\")\r\n\t\t\t} else {\r\n\t\t\t\tthis.showPopup = true; // 显示弹出窗\r\n\t\t\t\tconsole.log(\"没有登录\")\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t\r\n\t\t\tthis.resId = options.id\r\n\t\t\tthis.getReporttDeail(options.id);\r\n\t\t\tthis.getResComment(this.resId)\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t  \r\n\t\t},\r\n\t\t onShow() {\r\n\t\t    // 监听下拉刷新事件\r\n\t\t    uni.startPullDownRefresh();\r\n\t\t  },\r\n\t\t  onHide() {\r\n\t\t      // 停止监听下拉刷新事件\r\n\t\t      uni.stopPullDownRefresh();\r\n\t\t    },\r\n\t\tmethods: {\r\n\t\t\t onPullDownRefresh() {\r\n\t\t\t\t this.getReporttDeail(this.resId);\r\n\t\t\t\t this.getResComment(this.resId)\r\n\t\t\t      // 在这里处理下拉刷新事件\r\n\t\t\t      console.log('下拉刷新触发');\r\n\t\t\t\r\n\r\n\t\t\t\r\n\t\t\t      // 停止下拉刷新动画\r\n\t\t\t      uni.stopPullDownRefresh();\r\n\t\t\t    },\r\n\t\t\thidePopup() {\r\n\t\t\t\tthis.showPopup = false;\r\n\t\t\t},\r\n\t\t\tclosePopupTwo() {\r\n\t\t\t\tthis.showPopupTwo = false;\r\n\t\t\t},\r\n\t\t\tbackHome() {\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: '/pages/index'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tonShareAppMessage(res) {\r\n\t\t\t\tthis.shareForm.userId = this.userId\r\n\t\t\t\tthis.shareForm.bizId = this.resId\r\n\t\t\t\tthis.shareForm.path = '干货详情'\r\n\t\t\t\tsaveUserShare(this.shareForm).then(res => {\r\n\t\t\t\t\tthis.$modal.showToast(res.data)\r\n\t\t\t\t})\r\n\t\t\t\treturn {\r\n\t\t\t\t\ttitle: '干货分享',\r\n\t\t\t\t\tpath: `/pages/user/detail/index?id=` + this.resId\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tcopyToClipboard() {\n\t\t\t\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\tdata: this.resInfo.res_url,\r\n\t\t\t\t\tsuccess: function() {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '复制成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function() {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '复制失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\n\t\t\t\tthis.browseForm.userId = this.userId;\n\t\t\t\tthis.browseForm.id = this.resId;\n\t\t\t\t\n\t\t\t\t\n\t\t\t\tsaveUserDown(this.browseForm).then(res => {\n\t\t\t\t\tthis.$modal.closeLoading()\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\thandleConfirm(e) {\r\n\t\t\t\tthis.$modal.loading(\"发送评论中，请耐心等待...\")\r\n\t\t\t\tthis.comment.comment = this.usercomment;\r\n\t\t\t\tthis.comment.userId = this.userId;\r\n\t\t\t\tthis.comment.resId = this.resId;\r\n\t\t\t\tif (this.usercomment != '') {\r\n\t\t\t\t\tsaveUserComment(this.comment).then(res => {\r\n\t\t\t\t\t\tthis.$modal.closeLoading()\r\n\t\t\t\t\t\tthis.$modal.showToast('您的评论正在审核中，审核通过后会显示在评论区哦~')\r\n\t\t\t\t\t\tthis.usercomment = ''\r\n\t\t\t\t\t\tthis.getResComment(this.resId)\r\n\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttoggleLike(item) {\r\n\t\t\t\tconsole.log(item)\r\n\t\t\t\tif (item.isLike === 1) {\r\n\t\t\t\t\t// 如果 isLike 为 1，则 like 减 1，并且 isLike 变为 0\r\n\t\t\t\t\titem.userlike -= 1;\r\n\t\t\t\t\titem.isLike = 0;\r\n\t\t\t\t\tthis.$modal.showToast('已取消点赞')\r\n\t\t\t\t} else if (item.isLike === 0) {\r\n\t\t\t\t\t// 如果 isLike 大于 1，则 like 加 1，并且 isLike 变为 1\r\n\t\t\t\t\titem.userlike += 1;\r\n\t\t\t\t\titem.isLike = 1;\r\n\t\t\t\t\tthis.$modal.showToast('已成功点赞')\r\n\t\t\t\t}\r\n\t\t\t\titem.creator = this.userId;\r\n\t\t\t\tchangeCommentStatus(item).then(res => {\r\n\r\n\t\t\t\t})\r\n\r\n\t\t\t},\r\n\t\t\tgetResComment(resId) {\r\n\t\t\t\tgetResComment(resId).then(res => {\r\n\t\t\t\t\tthis.commentItems = res.data\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetReporttDeail(id) {\r\n\t\t\t\tgetReporttDeail(id).then(res => {\r\n\t\t\t\t\tthis.resInfo = res.data\r\n\t\t\t\t\tthis.browseForm.userId = this.userId;\r\n\t\t\t\t\tthis.browseForm.id = this.resId;\r\n\t\t\t\t\t// 阅读记录\r\n\t\t\t\t\tthis.$modal.closeLoading()\r\n\t\t\t\t\tsaveUserBrowse(this.browseForm).then(res => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonListItemClick(item) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/user/detail/index?id=' + item.id\r\n\t\t\t\t});\r\n\t\t\t\tconsole.log('点击了列表项', item);\r\n\t\t\t},\r\n\t\t\tmoreReport() {\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: '/pages/index'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetUserLike(userId) {\r\n\t\t\t\tgetUserLike(userId).then(res => {\r\n\t\t\t\t\tthis.listItems = res.data\r\n\t\t\t\t\tthis.listItems.forEach(item => {\r\n\t\t\t\t\t\titem.tags = item.tag.split(',').map(tagText => ({\r\n\t\t\t\t\t\t\ttext: tagText,\r\n\t\t\t\t\t\t\tcolor: this.getRandomColor(),\r\n\t\t\t\t\t\t}));\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetRandomColor() {\r\n\t\t\t\t// 预定义的颜色集合\r\n\t\t\t\tconst colors = ['#87CEEB', '#66CDAA', '#CD5C5C', '#DDA0DD', '#FFDAB9', '#C1CDC1', '#D1EEEE',\r\n\t\t\t\t\t'#EED2EE', '#9F79EE'\r\n\t\t\t\t];\r\n\t\t\t\t// 从集合中随机选择一个颜色\r\n\t\t\t\treturn colors[Math.floor(Math.random() * colors.length)];\r\n\t\t\t},\r\n\t\t\tpreviewDoc() {\r\n\t\t\t\twx.getSystemInfo({\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log('res:', res)\r\n\t\t\t\t\t\tif (res.environment) {\r\n\t\t\t\t\t\t\tthis.$modal.loading(\"加载中，请耐心等待...\")\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tconsole.log(this.resInfo.res_url)\r\n\t\t\t\t\t\t\tthis.browseForm.userId = this.userId;\r\n\t\t\t\t\t\t\tthis.browseForm.id = this.resId;\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tsaveUserDown(this.browseForm).then(res => {\r\n\t\t\t\t\t\t\t\tthis.$modal.closeLoading()\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: '/pages/user/detail/pdfview?url=' + encodeURIComponent(this.resInfo.res_url)\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthis.showPopupTwo =true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tasync wxhandleLogin() {\r\n\r\n\r\n\t\t\t\tthis.$modal.loading(\"登录中，请耐心等待...\")\r\n\t\t\t\twx.getSystemInfo({\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log('res:', res)\r\n\t\t\t\t\t\tif (res.environment) {\r\n\r\n\t\t\t\t\t\t\twx.login({\r\n\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"微信code\" + res.code)\r\n\r\n\t\t\t\t\t\t\t\t\tthis.wxLoginForm.code = res.code\r\n\t\t\t\t\t\t\t\t\twx.qy.login({\r\n\t\t\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log(\"企业微信\" + JSON.stringify(\r\n\t\t\t\t\t\t\t\t\t\t\t\tres))\r\n\t\t\t\t\t\t\t\t\t\t\tif (res.code) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.log(\"企业微信code=\" + res.code)\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.wxLoginForm.qwcode = res.code\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.sendWxLoginFormToLocalService(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t'qywx')\r\n\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.log('登录失败！' + res.errMsg)\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\r\n\r\n\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.getUserProfile({\r\n\t\t\t\t\t\t\t\tlang: 'zh_CN',\r\n\t\t\t\t\t\t\t\tdesc: '用于完善会员资料',\r\n\t\t\t\t\t\t\t\tsuccess: (user) => {\r\n\t\t\t\t\t\t\t\t\tthis.wxLoginForm.nickName = user.userInfo.nickName\r\n\t\t\t\t\t\t\t\t\tthis.wxLoginForm.avatar = user.userInfo.avatarUrl\r\n\t\t\t\t\t\t\t\t\tuni.getProvider({\r\n\t\t\t\t\t\t\t\t\t\tservice: 'oauth',\r\n\t\t\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\t\t\tif (~res.provider.indexOf(\"weixin\")) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.login({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tprovider: \"weixin\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsuccess: (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tloginRes) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.wxLoginForm\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.code =\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tloginRes\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.code\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tuni.getUserInfo({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsuccess: (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tinfoRes\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.wxLoginForm\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.encryptedIv =\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tinfoRes\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.iv\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.wxLoginForm\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.encryptedData =\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tinfoRes\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.encryptedData\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.sendWxLoginFormToLocalService(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'wx'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail(res) {\r\n\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\r\n\r\n\t\t\t},\r\n\t\t\tsendWxLoginFormToLocalService(env) {\r\n\t\t\t\tif (env == 'wx') {\r\n\t\t\t\t\tthis.$store.dispatch('WxLogin', this.wxLoginForm).then(() => {\r\n\t\t\t\t\t\tthis.$modal.closeLoading()\r\n\t\t\t\t\t\tthis.loginSuccess()\r\n\t\t\t\t\t\tconsole.log('登录成功')\r\n\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\tonsole.log('登录失败')\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (env == 'qywx') {\r\n\t\t\t\t\tthis.$store.dispatch('QyWxLogin', this.wxLoginForm).then(() => {\r\n\t\t\t\t\t\tthis.$modal.closeLoading()\r\n\t\t\t\t\t\tthis.loginSuccess()\r\n\t\t\t\t\t\tconsole.log('企业微信登录成功')\r\n\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\tconsole.log('企业微信登录失败')\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\r\n\t\t\t},\r\n\t\t\t// 登录成功后，处理函数\r\n\t\t\tloginSuccess(result) {\r\n\t\t\t\t// 设置用户信息\r\n\t\t\t\tthis.$store.dispatch('GetInfo').then(res => {\r\n\t\t\t\t\tthis.$tab.reLaunch('/pages/user/detail/index?id=' + this.resId)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.top {\r\n\t\tbackground-color: white;\r\n\t}\r\n\r\n\t.toptitle {\r\n\t\tbackground-color: white;\r\n\t\twidth: 100%;\r\n\t\tpadding: 40rpx;\r\n\t\tfont-size: 40rpx;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 10px;\r\n\t\tborder: 0px;\r\n\t}\r\n\r\n\t.info-container {\r\n\t\tbackground-color: white;\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding-left: 40rpx;\r\n\t\tpadding-right: 40rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.preview {\r\n\t\tbackground-color: white;\r\n\t\tpadding-left: 40rpx;\r\n        padding-top: 40rpx;\r\n\t\tpadding-bottom: 40rpx;\r\n\t}\r\n\r\n\t.comments {\r\n\t\twidth: 100%;\r\n\t\tborder-top: 1px solid #F5F5F5;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\r\n\t.usercomments {\r\n\t\tmargin-top: 30rpx;\r\n\t\twidth: 100%;\r\n\t\tborder-top: 1px solid #F5F5F5;\r\n\t\tpadding-bottom: 135rpx;\r\n\t\t/* 添加底部内边距，确保最后一条评论不会被遮挡 */\r\n\t}\r\n\r\n\t.comment {\r\n\t\tbackground-color: white;\r\n\t}\r\n\r\n\t.comment .list-item {\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\r\n\t.userLike {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 40rpx;\r\n\t\tbackground-color: white;\r\n\t}\r\n\r\n\t.userLike text {\r\n\r\n\t\tcolor: #8f8f94;\r\n\t\ttext-align: center;\r\n\r\n\t}\r\n\r\n\r\n\t.list-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tbackground-color: white;\r\n\t\tborder-bottom: 1px solid #F5F5F5;\r\n\t}\r\n\r\n\r\n\t.point-text {\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-left: 15rpx;\r\n\t}\r\n\r\n\t.tags {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\r\n\t.tag {\r\n\t\tpadding: 4px 8px;\r\n\t\tmargin: 4px;\r\n\t\tborder-radius: 4px;\r\n\t\tcolor: white;\r\n\t\tfont-size: 8px;\r\n\t}\r\n\r\n\r\n\r\n\t.likeIcon {\r\n\t\twidth: 30rpx;\r\n\t\theight: 30rpx;\r\n\t\tmargin-left: 400rpx;\r\n\t}\r\n\r\n\t.bottomIcon {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t\tmargin-left: 10rpx;\r\n\t}\r\n\r\n\t.downIcon {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t\tmargin-right: 95rpx;\r\n\t\tmargin-top: 6rpx;\r\n\t}\r\n\r\n\t.indexIcon {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t\tmargin-top: 5rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t.user-info {\r\n\t\tdisplay: flex;\r\n\t\t\r\n\t}\r\n\t\r\n\t.avatar {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t\tborder-radius: 40vh;\r\n\t\tmargin-right: 10px;\r\n\t}\r\n\r\n\t.nick-name {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tdisplay: inline-block;\r\n\t\tmax-width: 27%;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t\tcolor: black;\r\n\t}\r\n\r\n\t.like-section {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.like-count {\r\n\t\tmargin-left: 30vh;\r\n\t}\r\n\r\n\t.comment-content {\r\n\t\tmargin-top: 10px;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.comment-time {\r\n\t\tmargin-top: 10px;\r\n\t\tcolor: #8f8f94;\r\n\t}\r\n\r\n\t.comment-box {\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 150rpx;\r\n\t\tz-index: 1;\r\n\t\tbackground-color: white;\r\n\t\tbox-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.search-bar {\r\n\t\tpadding: 30rpx;\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\t.search-bar input {\r\n\t\ttext-align: center;\r\n\t\twidth: 700rpx;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t}\r\n\r\n\r\n\t.resUrl {\r\n\t\tdisplay: inline-block;\r\n\t\tmax-width: 70%;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t\tcolor: black;\r\n\t}\r\n\r\n\t.title {\r\n\t\tdisplay: inline-block;\r\n\t\tmax-width: 90%;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\t.shareBtn {\r\n\t\tmargin-top: -10rpx;\r\n\t\tbackground-color: white;\r\n\t}\r\n\r\n\t.shareBtn::after {\r\n\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t/* ...其他样式... */\r\n\r\n\t.popup-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground: rgba(0, 0, 0, 0.5);\r\n\t\tz-index: 999;\r\n\t}\r\n\r\n\t.popup-container {\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\tbackground: white;\r\n\t\tz-index: 1000;\r\n\t\ttransition: transform 0.3s ease-in-out;\r\n\t}\r\n\r\n\t.popup-content image {\r\n\t\tmargin-top: 100rpx;\r\n\t\tmargin-bottom: 50rpx;\r\n\t\tmargin-left: 280rpx;\r\n\t}\r\n\r\n\t.popup-content .descAuth {\r\n\t\tpadding: 50rpx;\r\n\t}\r\n\r\n\t.popup-content button {\r\n\t\tmargin-top: 20rpx;\r\n\t\tmargin-bottom: 50rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1737420405752\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}