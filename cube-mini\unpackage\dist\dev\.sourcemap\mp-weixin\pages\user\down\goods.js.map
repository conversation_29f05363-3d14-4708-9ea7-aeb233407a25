{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/user/down/goods.vue?b8c2", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/user/down/goods.vue?ce39", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/user/down/goods.vue?76e4", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/user/down/goods.vue?5c89", "uni-app:///pages/user/down/goods.vue", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/user/down/goods.vue?af7a", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/user/down/goods.vue?2d82"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "triggered", "searchValue", "selectedIndex", "tabItems", "name", "total", "hasMore", "downForm", "userId", "key<PERSON>ord", "pageSize", "pageIndex", "start", "end", "type", "listItems", "mounted", "onLoad", "created", "methods", "getUserReportList", "item", "text", "color", "onPulling", "setTimeout", "that", "onRefresh", "console", "onScrollToLower", "loadMore", "onSearchInput", "onTabItemClick", "onListItemClick", "getRandomColor"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACmN;AACnN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAk1B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACkCt2B;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;QACAC;MACA,GACA;QACAA;MACA,GACA;QACAA;MACA,EACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACA;MAAA;IAEA;EACA;EACAC,6BAEA;EACAC;IACA;IACA;EACA;EACAC,6BACA;EACAC;IACAC;MAAA;MACA;QACA;UACA;UACA;YACAC;cAAA;gBACAC;gBACAC;cACA;YAAA;UACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;QACA;QACAC;UACAC;QACA;MACA;IACA;IACAC;MACAC;MACA;MACA;IAEA;IACAC;MACAD;MACA;QACA;MACA;IACA;IACAE;MACA;QACA;QACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;UACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACAJ;IACA;IACAK;MACAL;IACA;IACAM;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACtJA;AAAA;AAAA;AAAA;AAAqsC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACAztC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/down/goods.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/user/down/goods.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./goods.vue?vue&type=template&id=316d5284&\"\nvar renderjs\nimport script from \"./goods.vue?vue&type=script&lang=js&\"\nexport * from \"./goods.vue?vue&type=script&lang=js&\"\nimport style0 from \"./goods.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/down/goods.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goods.vue?vue&type=template&id=316d5284&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goods.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goods.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"mine-container\" :style=\"{height: `${windowHeight}px`}\">\r\n\t\t<view class=\"content\">\r\n\t\t\t<!-- 搜索框 -->\r\n\t\t\t<view class=\"search-bar\">\r\n\t\t\t\t<input type=\"text\" placeholder=\"请输入搜索关键词\" @input=\"onSearchInput\" />\r\n\t\t\t</view>\r\n \r\n\r\n\r\n\r\n\t\t\t<!-- 列表 -->\r\n\t\t\t<scroll-view  scroll-y class=\"list\" @scrolltolower=\"onScrollToLower\" refresher-enabled\r\n\t\t\t\t:refresher-triggered=\"triggered\" @refresherpulling=\"onPulling\" @refresherrefresh=\"onRefresh\">\r\n\t\t\t\t<view class=\"list-item\" v-for=\"(item, index) in listItems\" :key=\"index\" @click=\"onListItemClick(item)\">\r\n\t\t\t\t\t<view class=\"point-text\">\r\n\t\t\t\t\t\t<view class=\"title\">{{ item.title }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t <view class=\"tags\">\r\n\t\t\t\t\t        <view class=\"tag\" v-for=\"(tag, tagIndex) in item.tags\" :key=\"tagIndex\" :style=\"{ backgroundColor: tag.color }\">\r\n\t\t\t\t\t          {{ tag.text }}\r\n\t\t\t\t\t        </view>\r\n\t\t\t\t\t     </view>\r\n\t\t\t\t\t<view style=\"color: #8f8f94;display:flex;justify-content: space-between;\" class=\"point-text\">\r\n\t\t\t\t\t\t<text>{{ item.create_time }}</text>\r\n\t\t\t\t\t\t<text>{{ item.resource }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport storage from '@/utils/storage'\r\n\timport constant from '@/utils/constant'\r\n\timport {\r\n\t\tgetUserReportList\r\n\t} from '@/api/report'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttriggered: false,\r\n\t\t\t\tsearchValue: '',\r\n\t\t\t\tselectedIndex: 0, // 新增，用于跟踪当前选中的标签索引\r\n\t\t\t\ttabItems: [{\r\n\t\t\t\t\t\tname: '热门推荐'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '热门行业'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '查看全部'\r\n\t\t\t\t\t},\r\n\t\t\t\t],\r\n\t\t\t\ttotal: '',\r\n\t\t\t\thasMore: true,\r\n\t\t\t\tdownForm: {\r\n\t\t\t\t\tuserId: storage.get(constant.userId),\r\n\t\t\t\t\tkeyWord: '',\r\n\t\t\t\t\tpageSize: 10,\r\n\t\t\t\t\tpageIndex: 1,\r\n\t\t\t\t\tstart: '',\r\n\t\t\t\t\tend: '',\r\n\t\t\t\t\ttype: ''\r\n\t\t\t\t},\r\n\t\t\t\tlistItems: [\r\n\t\t\t\t\t// 这里添加列表项的数据\r\n\t\t\t\t],\r\n\t\t\t};\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t \r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\tthis.downForm.type = options.type; // 输出：1\r\n\t\tthis.getUserReportList();\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetUserReportList() {\r\n\t\t\t\tgetUserReportList(this.downForm).then(res => {\r\n\t\t\t\t\tif (res.data && res.data.list) {\r\n\t\t\t\t\t\tthis.listItems = this.listItems.concat(res.data.list);\r\n\t\t\t\t\t\tthis.listItems.forEach(item => {\r\n\t\t\t\t\t\t     item.tags = item.tag.split(',').map(tagText => ({\r\n\t\t\t\t\t\t            text: tagText,\r\n\t\t\t\t\t\t            color: this.getRandomColor(),\r\n\t\t\t\t\t\t          }));\r\n\t\t\t\t\t\t   });\r\n\t\t\t\t\t\tthis.total = res.data.total;\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonPulling() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif (!this.triggered) {\r\n\t\t\t\t\t//下拉加载，先让其变true再变false才能关闭\r\n\t\t\t\t\tthis.triggered = true;\r\n\t\t\t\t\t//关闭加载状态 (转动的圈)，需要一点延时才能关闭\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthat.triggered = false;\r\n\t\t\t\t\t}, 1000)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tonRefresh() {\r\n\t\t\t\tconsole.log(\"下拉刷新\");\r\n\t\t\t\tthis.hasMore = true;\r\n\t\t\t\tthis.downForm.pageIndex = 1;\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tonScrollToLower() {\r\n            console.log(\"1111\")\r\n\t\t\t\tif (this.hasMore) {\r\n\t\t\t\t\tthis.loadMore();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tloadMore() {\r\n\t\t\t\tif (this.downForm.pageIndex * this.downForm.pageSize >= this.total) {\r\n\t\t\t\t\tthis.hasMore = false;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.downForm.pageIndex++;\r\n\t\t\t\t//this.getUserPointsRecord();\r\n\t\t\t},\r\n\t\t\tonSearchInput(e) {\r\n\t\t\t\tthis.searchValue = e.detail.value;\r\n\t\t\t\tthis.downForm.keyWord = e.detail.value;\r\n\t\t\t\tgetUserReportList(this.downForm).then(res => {\r\n\t\t\t\t\tif (res.data && res.data.list) {\r\n\t\t\t\t\t\tthis.listItems = res.data.list;\r\n\t\t\t\t\t\tthis.total = res.data.total;\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonTabItemClick(index) {\r\n\t\t\t\tthis.selectedIndex = index; // 更新选中的标签索引\r\n\t\t\t\tconsole.log('点击了', this.tabItems[index].name);\r\n\t\t\t},\r\n\t\t\tonListItemClick(item) {\r\n\t\t\t\tconsole.log('点击了列表项', item);\r\n\t\t\t},\r\n\t\t\tgetRandomColor() {\r\n\t\t\t       // 预定义的颜色集合\r\n\t\t\t          const colors = ['#FAF0E6', '#87CEEB', '#66CDAA', '#CD5C5C', '#DDA0DD','#C1CDC1','#D1EEEE','#EED2EE','#9F79EE'];\r\n\t\t\t          // 从集合中随机选择一个颜色\r\n\t\t\t          return colors[Math.floor(Math.random() * colors.length)];\r\n\t\t\t    },\r\n\t\t},\r\n\t};\r\n</script>\r\n\r\n<style>\r\n\t.content {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.search-bar {\r\n\t\tpadding: 10rpx;\r\n\t\twidth: 95%;\r\n\t\tmargin-top: 15rpx;\r\n\t\tbackground-color: white;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\r\n\r\n\t\r\n\r\n\t.list {\r\n\t\theight: 94vh;\r\n\t\t/* 根据实际情况调整高度 */\r\n\t\toverflow-y: auto;\r\n\t\t/* 确保内容超出时可以滚动 */\r\n\t\twidth: 99%;\r\n\t\tpadding: 10rpx;\r\n\r\n\t\tborder-radius: 5rpx;\r\n\t}\r\n\r\n\t.list-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tbackground-color: white;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\t.point-text {\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\t.tags {\r\n\t  display: flex;\r\n\t  flex-wrap: wrap;\r\n\t  margin-left: 20rpx;\r\n\t}\r\n\t.tag {\r\n\t  padding: 4px 8px;\r\n\t  margin: 4px;\r\n\t  border-radius: 4px;\r\n\t  color: white;\r\n\t  font-size: 8px;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goods.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goods.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1737420401331\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}