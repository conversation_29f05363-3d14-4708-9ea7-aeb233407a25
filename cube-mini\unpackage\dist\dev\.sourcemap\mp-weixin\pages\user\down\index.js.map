{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/user/down/index.vue?5ccb", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/user/down/index.vue?6ae8", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/user/down/index.vue?ff23", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/user/down/index.vue?cf34", "uni-app:///pages/user/down/index.vue", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/user/down/index.vue?f5ff", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/pages/user/down/index.vue?1cc6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "triggered", "searchValue", "selectedIndex", "tabItems", "name", "id", "total", "hasMore", "downForm", "userId", "key<PERSON>ord", "pageSize", "pageIndex", "start", "end", "type", "contentType", "listItems", "mounted", "onLoad", "created", "methods", "getUserReportList", "item", "text", "color", "console", "onPulling", "setTimeout", "that", "onRefresh", "onScrollToLower", "loadMore", "onSearchInput", "onTabItemClick", "onListItemClick", "getRandomColor"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACmN;AACnN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAk1B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC2Ct2B;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACA;MAAA;IACA,cACA;EAEA;EACAC,6BAEA;EACAC;IACA;IACA;EACA;EACAC;EACAC;IACAC;MAAA;MACA;QAEA;UACA;UACA;YACAC;cAAA;gBACAC;gBACAC;cACA;YAAA;UACA;UACAC;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;QACA;QACAC;UACAC;QACA;MACA;IACA;IACAC;MAAA;MACAJ;MACA;MACA;MACA;QACA;UACA;UACA;YACAH;cAAA;gBACAC;gBACAC;cACA;YAAA;UACA;UACAC;UACA;QACA;MACA;IACA;IACAK;MACAL;MACA;QACA;MACA;IACA;IACAM;MACA;QACA;QACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;UACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;QACA;UACA;YACA;YACA;cACAX;gBAAA;kBACAC;kBACAC;gBACA;cAAA;YACA;YACAC;YACA;UACA;QACA;QACA;MACA;QACA;QACA;UACA;YACA;YACA;cACAH;gBAAA;kBACAC;kBACAC;gBACA;cAAA;YACA;YACAC;YACA;UACA;QACA;QACA;MACA;IACA;IACAS;MACAT;IACA;IACAU;MACA;MACA,sGACA,UACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AChNA;AAAA;AAAA;AAAA;AAAqsC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACAztC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/down/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/user/down/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=a3a87400&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/down/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=a3a87400&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"mine-container\" :style=\"{height: `${windowHeight}px`}\">\r\n    <view class=\"content\">\r\n      <!-- 搜索框 -->\r\n      <!-- <view class=\"search-bar\">\r\n        <input type=\"text\" placeholder=\"请输入搜索关键词\" @input=\"onSearchInput\" />\r\n      </view> -->\r\n\r\n      <!-- Tab导航 -->\r\n      <view class=\"tab-nav\">\r\n        <view class=\"tab-item\" v-for=\"(item, index) in tabItems\" :key=\"index\"\r\n          :class=\"{ active: selectedIndex === index }\" @click=\"onTabItemClick(index)\">\r\n          {{ item.name }}\r\n        </view>\r\n      </view>\r\n      <!-- 列表 -->\r\n      <scroll-view scroll-y class=\"list\" @scrolltolower=\"onScrollToLower\" refresher-enabled\r\n        :refresher-triggered=\"triggered\" @refresherpulling=\"onPulling\" @refresherrefresh=\"onRefresh\">\r\n        <view class=\"list-item\" v-for=\"(item, index) in listItems\" :key=\"index\" @click=\"onListItemClick(item)\">\r\n          <view class=\"point-text\">\r\n            <view class=\"title\">{{ item.title }}</view>\r\n          </view>\r\n          <view class=\"tags\">\r\n            <view class=\"tag\" v-for=\"(tag, tagIndex) in item.tags\" :key=\"tagIndex\"\r\n              :style=\"{ backgroundColor: tag.color }\">\r\n              {{ tag.text }}\r\n            </view>\r\n          </view>\r\n          <view style=\"color: #8f8f94;display:flex;justify-content: space-between;\" class=\"point-text\">\r\n            <text>{{ item.create_time }}</text>\r\n            <text>{{ item.resource }}</text>\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n\r\n    </view>\r\n    <view v-if=\"total==0\" class=\"end-text\">\r\n      暂无数据~\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import storage from '@/utils/storage'\r\n  import constant from '@/utils/constant'\r\n  import {\r\n    getUserReportList\r\n  } from '@/api/report'\r\n  export default {\r\n    data() {\r\n      return {\r\n        triggered: false,\r\n        searchValue: '',\r\n        selectedIndex: 0, // 新增，用于跟踪当前选中的标签索引\r\n        tabItems: [{\r\n            name: '干货',\r\n            id: 1\r\n          },\r\n          {\r\n            name: '攻略',\r\n            id: 2\r\n          }\r\n        ],\r\n        total: '',\r\n        hasMore: true,\r\n        downForm: {\r\n          userId: storage.get(constant.userId),\r\n          keyWord: '',\r\n          pageSize: 10,\r\n          pageIndex: 1,\r\n          start: '',\r\n          end: '',\r\n          type: '',\r\n          contentType: 1\r\n        },\r\n        listItems: [\r\n          // 这里添加列表项的数据\r\n        ],\r\n        hasMore: true,\r\n      };\r\n    },\r\n    mounted() {\r\n\r\n    },\r\n    onLoad(options) {\r\n      this.downForm.type = options.type; // 输出：1\r\n      this.getUserReportList();\r\n    },\r\n    created() {},\r\n    methods: {\r\n      getUserReportList() {\r\n        getUserReportList(this.downForm).then(res => {\r\n\r\n          if (res.code == 200) {\r\n            this.listItems = this.listItems.concat(res.data.list);\r\n            this.listItems.forEach(item => {\r\n              item.tags = item.tag.split(',').map(tagText => ({\r\n                text: tagText,\r\n                color: this.getRandomColor(),\r\n              }));\r\n            });\r\n            console.log(this.listItems, \"resres\");\r\n            this.total = res.data.total;\r\n          }\r\n        })\r\n      },\r\n      onPulling() {\r\n        var that = this;\r\n        if (!this.triggered) {\r\n          //下拉加载，先让其变true再变false才能关闭\r\n          this.triggered = true;\r\n          //关闭加载状态 (转动的圈)，需要一点延时才能关闭\r\n          setTimeout(() => {\r\n            that.triggered = false;\r\n          }, 1000)\r\n        }\r\n      },\r\n      onRefresh() {\r\n        console.log(\"下拉刷新\");\r\n        this.hasMore = true;\r\n        this.downForm.pageIndex = 1;\r\n        getUserReportList(this.downForm).then(res => {\r\n          if (res.code == 200) {\r\n            this.listItems = res.data.list;\r\n            this.listItems.forEach(item => {\r\n              item.tags = item.tag.split(',').map(tagText => ({\r\n                text: tagText,\r\n                color: this.getRandomColor(),\r\n              }));\r\n            });\r\n            console.log(this.listItems, \"resres\");\r\n            this.total = res.data.total;\r\n          }\r\n        })\r\n      },\r\n      onScrollToLower() {\r\n        console.log(\"1111\")\r\n        if (this.hasMore) {\r\n          this.loadMore();\r\n        }\r\n      },\r\n      loadMore() {\r\n        if (this.downForm.pageIndex * this.downForm.pageSize >= this.total) {\r\n          this.hasMore = false;\r\n          return;\r\n        }\r\n        this.downForm.pageIndex++;\r\n        this.getUserReportList();\r\n      },\r\n      onSearchInput(e) {\r\n        this.searchValue = e.detail.value;\r\n        this.downForm.keyWord = e.detail.value;\r\n        getUserReportList(this.downForm).then(res => {\r\n          if (res.data && res.data.list) {\r\n            this.listItems = res.data.list;\r\n            this.total = res.data.total;\r\n          }\r\n        })\r\n      },\r\n      onTabItemClick(index) {\r\n        this.selectedIndex = index; // 更新选中的标签索引\r\n        this.$modal.loading(\"加载中，请耐心等待...\")\r\n        if (this.tabItems[index].name == '干货') {\r\n          this.downForm.contentType = 1\r\n          getUserReportList(this.downForm).then(res => {\r\n            if (res.code == 200) {\r\n              this.listItems = res.data.list;\r\n              this.listItems.forEach(item => {\r\n                item.tags = item.tag.split(',').map(tagText => ({\r\n                  text: tagText,\r\n                  color: this.getRandomColor(),\r\n                }));\r\n              });\r\n              console.log(this.listItems, \"resres\");\r\n              this.total = res.data.total;\r\n            }\r\n          })\r\n          this.$modal.closeLoading()\r\n        } else if (this.tabItems[index].name == '攻略') {\r\n          this.downForm.contentType = 2\r\n          getUserReportList(this.downForm).then(res => {\r\n            if (res.code == 200) {\r\n              this.listItems = res.data.list;\r\n              this.listItems.forEach(item => {\r\n                item.tags = item.tag.split(',').map(tagText => ({\r\n                  text: tagText,\r\n                  color: this.getRandomColor(),\r\n                }));\r\n              });\r\n              console.log(this.listItems, \"resres\");\r\n              this.total = res.data.total;\r\n            }\r\n          })\r\n          this.$modal.closeLoading()\r\n        }\r\n      },\r\n      onListItemClick(item) {\r\n        console.log('点击了列表项', item);\r\n      },\r\n      getRandomColor() {\r\n        // 预定义的颜色集合\r\n        const colors = ['#FAF0E6', '#87CEEB', '#66CDAA', '#CD5C5C', '#DDA0DD', '#C1CDC1', '#D1EEEE', '#EED2EE',\r\n          '#9F79EE'\r\n        ];\r\n        // 从集合中随机选择一个颜色\r\n        return colors[Math.floor(Math.random() * colors.length)];\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style>\r\n  .content {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n\r\n  .search-bar {\r\n    padding: 10rpx;\r\n    width: 95%;\r\n    margin-top: 15rpx;\r\n    background-color: white;\r\n    border-radius: 10rpx;\r\n  }\r\n\r\n  .list {\r\n    /* height: 94vh; */\r\n    /* 根据实际情况调整高度 */\r\n    overflow-y: auto;\r\n    /* 确保内容超出时可以滚动 */\r\n    width: 99%;\r\n    padding: 10rpx;\r\n    border-radius: 5rpx;\r\n  }\r\n\r\n  .list-item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    margin-bottom: 20rpx;\r\n    box-shadow: 0rpx 3rpx 13rpx 0rpx rgba(0, 0, 0, 0.06);\r\n    /* background-color: white; */\r\n    border-radius: 10rpx;\r\n  }\r\n\r\n  .point-text {\r\n    padding: 30rpx;\r\n  }\r\n\r\n  .tags {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    margin-left: 20rpx;\r\n  }\r\n\r\n  .tag {\r\n    padding: 4px 8px;\r\n    margin: 4px;\r\n    border-radius: 4px;\r\n    color: white;\r\n    font-size: 8px;\r\n  }\r\n\r\n  .tab-nav {\r\n    display: flex;\r\n    /* margin-top: 15rpx; */\r\n    width: 95%;\r\n    justify-content: space-around;\r\n    padding: 10rpx 0;\r\n    background-color: white;\r\n    border-radius: 5rpx;\r\n  }\r\n\r\n  .tab-item {\r\n    padding: 10rpx;\r\n    position: relative;\r\n    color: #000;\r\n  }\r\n\r\n  .tab-item.active {\r\n    color: royalblue;\r\n  }\r\n\r\n  .tab-item.active::after {\r\n    content: '';\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n    width: 50%;\r\n    height: 2rpx;\r\n    background-color: blue;\r\n  }\r\n\r\n  .end-text {\r\n    text-align: center;\r\n    padding: 20rpx;\r\n    color: #999;\r\n  }\r\n</style>", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1737420401341\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}