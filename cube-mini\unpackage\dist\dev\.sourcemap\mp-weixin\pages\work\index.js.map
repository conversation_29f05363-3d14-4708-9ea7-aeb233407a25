{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/JavaWorkSpace/U3W-AI/cube-mini/pages/work/index.vue?6ce7", "webpack:///D:/JavaWorkSpace/U3W-AI/cube-mini/pages/work/index.vue?7bfd", "webpack:///D:/JavaWorkSpace/U3W-AI/cube-mini/pages/work/index.vue?86e8", "uni-app:///pages/work/index.vue", "webpack:///D:/JavaWorkSpace/U3W-AI/cube-mini/pages/work/index.vue?a08f", "webpack:///D:/JavaWorkSpace/U3W-AI/cube-mini/pages/work/index.vue?5d33"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "userId", "corpId", "chatId", "expandedHistoryItems", "userInfoReq", "userPrompt", "taskId", "roles", "toneChatId", "ybDsChatId", "dbChatId", "isNewChat", "jsonRpcReqest", "jsonrpc", "id", "method", "params", "sectionExpanded", "aiConfig", "promptInput", "taskStatus", "aiList", "avatar", "capabilities", "selectedCapabilities", "enabled", "status", "progressLogs", "isExpanded", "label", "value", "taskStarted", "enabledAIs", "screenshots", "autoPlay", "results", "activeResultIndex", "chatHistory", "selectedResults", "scorePrompt", "collectNum", "socketTask", "reconnectTimer", "heartbeatTimer", "reconnectCount", "maxReconnectCount", "isConnecting", "shouldReconnect", "scrollIntoView", "historyDrawerVisible", "scoreModalVisible", "aiLoginStatus", "yuanbao", "do<PERSON>o", "agent", "accounts", "isLoading", "computed", "canSend", "canScore", "console", "currentResult", "groupedHistory", "chatGroups", "Object", "chatGroup", "groups", "parentItem", "isParent", "children", "child", "onLoad", "uni", "title", "content", "showCancel", "confirmText", "success", "url", "onUnload", "methods", "initUserInfo", "generateUUID", "toggleSection", "toggleAI", "ai", "toggleCapability", "sendPrompt", "icon", "initWebSocket", "fail", "clearTimeout", "duration", "handleReconnect", "startHeartbeat", "type", "timestamp", "stopHeartbeat", "clearInterval", "sendWebSocketMessage", "message", "closeWebSocket", "handleWebSocketMessage", "targetAI", "isCompleted", "wkpfAI", "aiName", "shareUrl", "shareImgUrl", "handleAiStatusMessage", "handleAIResult", "getStatusText", "getStatusIconClass", "getStatusEmoji", "toggleTaskExpansion", "toggleAutoPlay", "previewImage", "current", "urls", "switchResultTab", "renderMarkdown", "isImageFile", "isPdfFile", "copyResult", "collectToOffice", "contentText", "num", "res", "exportResult", "openShareUrl", "copyPdfUrl", "openPdfFile", "filePath", "showHistoryDrawer", "closeHistoryDrawer", "loadChatHistory", "loadHistoryItem", "loadLastChat", "lastChat", "saveHistory", "historyData", "getHistoryDate", "date", "year", "month", "day", "hour", "minute", "second", "parseInt", "yesterday", "formatHistoryTime", "toggleHistoryExpansion", "showScoreModal", "closeScoreModal", "toggleResultSelection", "handleScore", "filter", "map", "join", "createNewChat", "checkAiLoginStatus", "setTimeout", "sendAiStatusCheck", "getPlatformIcon", "getPlatformName", "refreshAiStatus", "isAiLoginEnabled", "isAiInLoading", "disableAIsByLoginStatus", "updateAiEnabledStatus", "formatTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AAC+K;AAC/K,gBAAgB,sLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzIA;AAAA;AAAA;AAAA;AAA8pB,CAAgB,4pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;ACyRlrB;AAGA;AAGA;AAGA;AACA;AAAA;AAAA;AAAA,eAGA;EACAC;EACAC;IACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAL;QACAC;QACAK;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;QACAvB;QACAwB;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACA9B;QACAwB;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACA9B;QACAwB;QACAC;UACAM;UACAC;QACA,GACA;UACAD;UACAC;QACA,EACA;QACAN;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACA9B;QACAwB;QACAC;UACAM;UACAC;QACA,GACA;UACAD;UACAC;QACA,EACA;QACAN;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACA9B;QACAwB;QACAC;UACAM;UACAC;QACA;QACAN;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MAEA;MACAT;MACAY;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAH;QACAC;QACAC;MACA;MACAE;QACAJ;QACAC;QACAC;MACA;IACA;EACA;EAEAG;IACAC;MAAA;MACA;MACA;;MAEA;MACA;QAAA;MAAA;;MAEA;MACA;MAEA;IACA;IAEAC;MACA;MACA;MACAC;MACAA;MACAA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MAAA;MACA;MACA;;MAEA;MACA;QACA;UACAC;QACA;QACAA;MACA;;MAEA;MACAC;QACA;QACAC;UAAA;QAAA;;QAEA;QACA;QACA;QAEA;UACAC;QACA;;QAEA;QACAA,kDACAC;UACAC;UACAxC;UACAyC;YAAA,uCACAC;cACAF;YAAA;UAAA,CACA;QAAA,GACA;MACA;MAEA;IACA;EACA;EACAG;IACA;;IAEA;IACA;MACAX;MACAY;QACAC;QACAC;QACAC;QACAC;QACAC;UACAL;YACAM;UACA;QACA;MACA;MACA;IACA;IAEA;IACA;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;MACA;MAEA;;MAEA;MACA;MACA;MAEArB;QACA5D;QACAC;MACA;IACA;IAEA;IACAiF;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAZ;UACAC;UACAC;UACAC;UACAC;QACA;QACA;MACA;MACAS;IACA;IAEA;IACAC;MACA;MACA;QACAd;UACAC;UACAC;UACAC;UACAC;QACA;QACA;MACA;MAEA;MAEA;MACA;QACAS;MACA;QACAA;MACA;IACA;IAEA;IACAE;MAAA;MACA;MAEA;MACA3B;MACA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;MACA;MACA;;MAEA;MACA;QAAA;MAAA;;MAEA;MACA;QACAyB;MACA;;MAEA;MACA;QACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;QACA;QACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;QACA;QACA;UACA;QACA;QACA;UACA;QACA;QACA;UACA;UACA;YACA;UACA;QACA;MACA;MAEAzB;;MAEA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MAEAY;QACAC;QACAe;MACA;IACA;IAIA;IACAC;MAAA;MACA;MACA;QACA7B;QACAY;UACAC;UACAC;UACAC;UACAC;UACAC;YACAL;cACAM;YACA;UACA;QACA;QACA;MACA;MAEA;QACAlB;QACA;MACA;;MAEA;MACA;QACAA;QACA;QACA;MACA;MAEA;MACA;;MAEA;MACA;MACAA;MAEA;QACAkB;QACAD;UACAjB;QACA;QACA8B;UACA9B;UACA;UACA;QACA;MACA;MAEA;QACAA;QACA;QACA;;QAEA;QACA;UACA+B;UACA;QACA;QAEAnB;UACAC;UACAe;UACAI;QACA;;QAEA;QACA;MACA;MAEA;QACA;MACA;MAEA;QACAhC;QACA;QACAY;UACAC;UACAe;QACA;QACA;MACA;MAEA;QACA5B;QACA;QACA;;QAEAY;UACAC;UACAe;QACA;;QAEA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAK;MAAA;MACA;QACAjC;QACAY;UACAC;UACAC;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACAe;QACA;MACA;MAEA;MACA;;MAEA/B;MAEA;QACAA;QACA;MACA;IACA;IAEA;IACAkC;MAAA;MACA;;MAEA;QACA;UACA;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACAC;QACA;MACA;IACA;IAEAC;MACA;QACA;UACApG;QACA;MACA;QACA6D;MACA;IACA;IAEA;IACAwC;MACA;QACA;UACA5B;YACAC;YACAe;YACAI;UACA;QACA;MACA;IACA;IAEAS;MACA;MACA;;MAEA;MACA;QACAV;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;QACA;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAW;MACA;QACA;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;;QAEA;QACA;UACA;YAAA;UAAA;UACA;YACA;YACAC;cACA7B;cACAsB;cACAQ;YACA;UACA;UACA;QACA;;QAEA;QACA;UACA5C;UACA;UACA;UACAA;UACA;UACA;UACAA;UACA;QACA;;QAEA;QACA;UACA;YAAA;UAAA;UACA;YACA6C;YACA;cACAA;YACA;YACA;YACA;cACAC;cACAhC;cACAiC;cACAC;cACAZ;YACA;YACA;;YAEA;YACA;YACA;YACA;;YAEA;YACA;UACA;UACA;QACA;;QAEA;QACA;;QAEA;QACA;MAEA;QACApC;MACA;IACA;IAEAiD;MACA;MACA;QACA;QACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;QACA;QACA;MACA;MACA;MAAA,KACA;QACA;QACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;QACA;QACA;MACA;MACA;MAAA,KACA;QACA;QACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;QACA;QACA;MACA;IACA;IAEAC;MACA;;MAEA;MACA;QACA;UACAlD;UACA2C;YAAA;UAAA;UACA;QACA;UACA3C;UACA2C;YAAA;UAAA;UACA;QACA;UACA3C;UACA2C;YAAA;UAAA;UACA;QACA;UACA3C;UACA2C;YAAA;UAAA;UACA;QACA;UACA3C;UACA2C;YAAA;UAAA;UACA;MAAA;MAGA;QACA;QACAA;;QAEA;QACA;UACAA;QACA;;QAEA;QACA;UAAA;QAAA;QACA;UACA;YACAG;YACAhC;YACAiC;YACAC;YACAZ;UACA;UACA;QACA;UACA;UACA;YACAU;YACAhC;YACAiC;YACAC;YACAZ;UACA;UACA;QACA;;QAEA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;MACA;IACA;IAEA;IACAe;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA7B;IACA;IAEA;IACA8B;MACA;IACA;IAEA;IACAC;MACA5C;QACA6C;QACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEAC;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEAC;MACAnD;QACAzE;QACA8E;UACAL;YACAC;YACAe;UACA;QACA;MACA;IACA;IAEA;IACAoC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEApD;kBACAC;gBACA;;gBAEA;gBACA;gBAEAzD;kBACA6G;kBACA7H;kBACA2G;kBACAD;kBACAoB;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBAEAvD;gBAEA;kBACAA;oBACAC;oBACAe;kBACA;gBACA;kBACAhB;oBACAC;oBACAe;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAhB;gBACAZ;gBACAY;kBACAC;kBACAe;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEAwC;MACA;MACA;IACA;IAEAC;MACAzD;QACAzE;QACA8E;UACAL;YACAC;YACAe;UACA;QACA;QACAE;UACAlB;YACAC;YACAe;UACA;QACA;MACA;IACA;IAEA;IACA0C;MACA1D;QACAzE;QACA8E;UACAL;YACAC;YACAe;UACA;QACA;QACAE;UACAlB;YACAC;YACAe;UACA;QACA;MACA;IACA;IAEA;IACA2C;MACA3D;QACAC;MACA;;MAEA;MACAD;QACAM;QACAD;UACAL;UACA;YACA;YACAA;cACA4D;cACAvD;gBACAL;kBACAC;kBACAe;gBACA;cACA;cACAE;gBACA;gBACAlB;kBACAC;kBACAC;kBACAC;kBACAE;oBACAL;sBACAzE;oBACA;kBACA;gBACA;cACA;YACA;UACA;YACAyE;cACAC;cACAe;YACA;UACA;QACA;QACAE;UACAlB;UACA;UACAA;YACAC;YACAC;YACAC;YACAE;cACAL;gBACAzE;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAsI;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAR;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAnE;gBACAY;kBACAC;kBACAe;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAgD;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;;QAEA;QACA;QACA;QACA;QACA;QAEA;QACAhE;UACAC;UACAe;QACA;MACA;QACA5B;QACAY;UACAC;UACAe;QACA;MACA;IACA;IAEA;IACAiD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAV;gBACA;kBACA;kBACAW;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA9E;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA+E;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACAvH;kBACAF;kBACAa;kBACAC;kBACAE;kBACAjC;kBACAM;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;gBAAA,OAGA;kBACAV;kBACAK;kBACAN;kBACAG;kBACAM;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAkD;gBACAY;kBACAC;kBACAe;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAqD;MACA;QACAjF;QAEA;UACA;QACA;QAEA;QAEA;UACAkF;QACA;UACA;UACA;UACA;YACA;cAAAC;cAAAC;cAAAC;cAAAC;cAAAC;cAAAC;YACAN,gBACAO,gBACAA,qBACAA,eACAA,gBACAA,kBACAA,iBACA;UACA;YACA;YACA;YACAP;YAEA;cACAA;YACA;UACA;QACA;UACAA;QACA;QAEAlF;QAEA;UACA;QACA;QAEA;QACA;QACA0F;QAEA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA1F;QACA;MACA;IACA;IAEA;IACA2F;MACA;QACA3F;QAEA;QAEA;UACA;QACA;;QAEA;QACA;UACAkF;QACA;UACA;UACA;YACAA;UACA;UACA;UAAA,KACA;YACA;YACA;cACA;gBAAAC;gBAAAC;gBAAAC;gBAAAC;gBAAAC;gBAAAC;cACA;cACAN,gBACAO,gBACAA,qBACAA,eACAA,iBACAA,mBACAA,iBACA;YACA;cACA;cACA;cACAP;cAEA;gBACAA;cACA;YACA;UACA;QACA;UACAA;QACA;UACAA;QACA;QAEAlF;QAEA;UACA;QACA;;QAEA;QACA;QACA;QAEA;QAEAA;QACA;MAEA;QACAA;QACA;MACA;IACA;IAEA;IACA4F;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;MACA/F;MACAA;MACA;MACAA;MACAA;IACA;IAEAgG;MAAA;MACA;;MAEA;MACA,oCACAC;QAAA;MAAA,GACAC;QACA;QACA;QACA;MACA,GACAC;;MAEA;MACA;;MAEA;MACA;QACAlJ;QACAC;QACAC;QACAC;UACAV;UACAN;UACAC;UACAI;UACAE;QACA;MACA;;MAEA;MACAqD;MACA;MACA;;MAEA;MACA;QACA9D;QACAwB;QACAC;QACAC;QACAC;QACAC;QACAC,eACA;UACA+C;UACAsB;UACAQ;UACAT;QACA,EACA;QACAnE;MACA;;MAEA;MACA;QAAA;MAAA;MACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA4C;QACAC;QACAe;MACA;IACA;IAEA;IACAwE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA3J;QACAL;QACAC;QACAK;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACA;QACAb;QACAwB;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACA9B;QACAwB;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACA9B;QACAwB;QACAC;UACAM;UACAC;QACA,GACA;UACAD;UACAC;QACA,EACA;QACAN;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACA9B;QACAwB;QACAC;UACAM;UACAC;QACA,GACA;UACAD;UACAC;QACA,EACA;QACAN;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACA9B;QACAwB;QACAC;UACAM;UACAC;QACA;QACAN;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MACA;;MAEA;MACA;MACA;MACA;MAEA4C;QACAC;QACAe;MACA;IACA;IAEA;IACAyE;MAAA;MACA;MACAC;QACA;QACA;MACA;IACA;IAEAC;MACA;MACA;QACApE;QACA/F;QACAC;MACA;;MAEA;MACA;QACA8F;QACA/F;QACAC;MACA;;MAEA;MACA;QACA8F;QACA/F;QACAC;MACA;IACA;IAEAmK;MACA;QACAhH;QACAC;QACAC;MACA;MACA;IACA;IAEA+G;MACA;QACAjH;QACAC;QACAC;MACA;MACA;IACA;IAMAgH;MAAA;MACA;MACA;QACAlH;QACAC;QACAC;MACA;;MAEA;MACA;QACAF;QACAC;QACAC;MACA;;MAEA;MACA;QACAF;QACAC;QACAC;MACA;;MAEA;MACAkB;QACAC;QACAe;QACAI;MACA;;MAEA;MACA;MACAsE;QACA;QACA;QACAA;UACA;QACA;MACA;IACA;IAEA;IACAK;MACA;QACA;QACA;UACA;QAAA;QACA;QACA;UACA;QAAA;QACA;UACA;QAAA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;QACA;UACA;QACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA;MACA7G;IACA;IAEA;IACA8G;MACA;MACA9G;IACA;IAEA;IACA+G;MACA;QACA/G;QAEA;UACA;QACA;QAEA;QAEA;UACAkF;QACA;UACA;UACA;YACAA;UACA;UACA;UAAA,KACA;YACA;YACA;cACA;gBAAAC;gBAAAC;gBAAAC;gBAAAC;gBAAAC;gBAAAC;cACAN,gBACAO,gBACAA,qBACAA,eACAA,kBACAA,oBACAA,kBACA;YACA;cACA;cACA;cACAP;cAEA;gBACAA;cACA;YACA;UACA;QACA;UACAA;QACA;UACAA;QACA;QAEAlF;QAEA;UACA;QACA;;QAEA;QACA;QACA;QACA;QAEA;QAEAA;QACA;MAEA;QACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACz9DA;AAAA;AAAA;AAAA;AAAi+B,CAAgB,27BAAG,EAAC,C;;;;;;;;;;;ACAr/B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/work/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/work/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=51b5538d&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=51b5538d&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"51b5538d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/work/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=51b5538d&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 = _vm.sectionExpanded.aiConfig\n    ? _vm.__map(_vm.aiList, function (ai, index) {\n        var $orig = _vm.__get_orig(ai)\n        var m0 = ai.enabled && _vm.isAiLoginEnabled(ai)\n        var m1 = _vm.isAiLoginEnabled(ai)\n        var m2 = _vm.isAiLoginEnabled(ai)\n        var m3 =\n          !_vm.isAiLoginEnabled(ai) &&\n          !_vm.isLoading.yuanbao &&\n          !_vm.isLoading.doubao &&\n          !_vm.isLoading.agent\n        var m4 = _vm.isAiInLoading(ai)\n        var m5 = ai.enabled && _vm.isAiLoginEnabled(ai)\n        var m6 = !_vm.isAiLoginEnabled(ai) || _vm.isAiInLoading(ai)\n        var g0 = ai.capabilities.length\n        var l0 =\n          g0 > 0\n            ? _vm.__map(ai.capabilities, function (capability, __i0__) {\n                var $orig = _vm.__get_orig(capability)\n                var g1 = ai.selectedCapabilities.includes(capability.value)\n                var m7 = !ai.enabled || !_vm.isAiLoginEnabled(ai)\n                return {\n                  $orig: $orig,\n                  g1: g1,\n                  m7: m7,\n                }\n              })\n            : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n          m4: m4,\n          m5: m5,\n          m6: m6,\n          g0: g0,\n          l0: l0,\n        }\n      })\n    : null\n  var g2 = _vm.sectionExpanded.promptInput ? _vm.promptInput.length : null\n  var l3 =\n    _vm.taskStarted && _vm.sectionExpanded.taskStatus\n      ? _vm.__map(_vm.enabledAIs, function (ai, index) {\n          var $orig = _vm.__get_orig(ai)\n          var m8 = _vm.getStatusText(ai.status)\n          var m9 = _vm.getStatusIconClass(ai.status)\n          var m10 = _vm.getStatusEmoji(ai.status)\n          var g3 = ai.isExpanded && ai.progressLogs.length > 0\n          var l2 = g3\n            ? _vm.__map(ai.progressLogs, function (log, logIndex) {\n                var $orig = _vm.__get_orig(log)\n                var m11 = _vm.formatTime(log.timestamp)\n                return {\n                  $orig: $orig,\n                  m11: m11,\n                }\n              })\n            : null\n          return {\n            $orig: $orig,\n            m8: m8,\n            m9: m9,\n            m10: m10,\n            g3: g3,\n            l2: l2,\n          }\n        })\n      : null\n  var g4 = _vm.results.length\n  var m12 =\n    g4 > 0 && _vm.currentResult\n      ? _vm.currentResult.shareImgUrl &&\n        _vm.isImageFile(_vm.currentResult.shareImgUrl)\n      : null\n  var m13 =\n    g4 > 0 && _vm.currentResult && !m12\n      ? _vm.currentResult.shareImgUrl &&\n        _vm.isPdfFile(_vm.currentResult.shareImgUrl)\n      : null\n  var m14 =\n    g4 > 0 && _vm.currentResult && !m12 && !m13\n      ? _vm.renderMarkdown(_vm.currentResult.content)\n      : null\n  var l5 = _vm.historyDrawerVisible\n    ? _vm.__map(_vm.groupedHistory, function (group, date) {\n        var $orig = _vm.__get_orig(group)\n        var l4 = _vm.__map(group, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m15 = _vm.formatHistoryTime(item.createTime)\n          return {\n            $orig: $orig,\n            m15: m15,\n          }\n        })\n        return {\n          $orig: $orig,\n          l4: l4,\n        }\n      })\n    : null\n  var l6 = _vm.scoreModalVisible\n    ? _vm.__map(_vm.results, function (result, index) {\n        var $orig = _vm.__get_orig(result)\n        var g5 = _vm.selectedResults.includes(result.aiName)\n        return {\n          $orig: $orig,\n          g5: g5,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n        g2: g2,\n        l3: l3,\n        g4: g4,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        l5: l5,\n        l6: l6,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"console-container\">\r\n\t\t<!-- 顶部固定区域 -->\r\n\t\t<view class=\"header-fixed\">\r\n\t\t\t<view class=\"header-content\">\r\n\t\t\t\t<text class=\"header-title\">AI控制台</text>\r\n\t\t\t\t<view class=\"header-actions\">\r\n\t\t\t\t\t<view class=\"action-btn refresh-btn\" @tap=\"refreshAiStatus\">\r\n\t\t\t\t\t\t<image class=\"action-icon-img\" src=\"https://u3w.com/chatfile/shuaxin.png\" mode=\"aspectFit\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"action-btn history-btn\" @tap=\"showHistoryDrawer\">\r\n\t\t\t\t\t\t<image class=\"action-icon-img\" src=\"https://u3w.com/chatfile/lishi.png\" mode=\"aspectFit\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"action-btn new-chat-btn\" @tap=\"createNewChat\">\r\n\t\t\t\t\t\t<image class=\"action-icon-img\" src=\"https://u3w.com/chatfile/chuangjian.png\" mode=\"aspectFit\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\r\n\t\t</view>\r\n\r\n\t\t<!-- 主体滚动区域 -->\r\n\t\t<scroll-view class=\"main-scroll\" scroll-y :scroll-into-view=\"scrollIntoView\" :enhanced=\"true\" :bounces=\"true\"\r\n\t\t\t:show-scrollbar=\"false\" :fast-deceleration=\"false\">\r\n\r\n\t\t\t<!-- AI配置区块 -->\r\n\t\t\t<view class=\"section-block\" id=\"ai-config\">\r\n\t\t\t\t<view class=\"section-header\" @tap=\"toggleSection('aiConfig')\">\r\n\t\t\t\t\t<text class=\"section-title\">AI选择配置</text>\r\n\t\t\t\t\t<text class=\"section-arrow\">\r\n\t\t\t\t\t\t{{ sectionExpanded.aiConfig ? '▼' : '▶' }}\r\n\t\t\t\t\t</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"section-content\" v-if=\"sectionExpanded.aiConfig\">\r\n\t\t\t\t\t<view class=\"ai-grid\">\r\n\t\t\t\t\t\t<view v-for=\"(ai, index) in aiList\" :key=\"index\" class=\"ai-card\"\r\n\t\t\t\t\t\t\t:class=\"[ai.enabled && isAiLoginEnabled(ai) ? 'ai-enabled' : '', !isAiLoginEnabled(ai) ? 'ai-disabled' : '']\">\r\n\t\t\t\t\t\t\t<view class=\"ai-header\">\r\n\t\t\t\t\t\t\t\t<!-- <image class=\"ai-avatar\" :src=\"ai.avatar\" mode=\"aspectFill\" :class=\"[!isAiLoginEnabled(ai) ? 'avatar-disabled' : '']\"></image> -->\r\n\t\t\t\t\t\t\t\t<view class=\"ai-info\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"ai-name-container\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"ai-name\" :class=\"[!isAiLoginEnabled(ai) ? 'name-disabled' : '']\">{{\r\n\t\t\t\t\t\t\t\t\t\t\tai.name }}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\t\t\t\t\tv-if=\"!isAiLoginEnabled(ai) && !isLoading.yuanbao && !isLoading.doubao && !isLoading.agent\"\r\n\t\t\t\t\t\t\t\t\t\t\tclass=\"login-required\">需登录</text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-if=\"isAiInLoading(ai)\" class=\"loading-text\">检查中...</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<switch :checked=\"ai.enabled && isAiLoginEnabled(ai)\"\r\n\t\t\t\t\t\t\t\t\t\t:disabled=\"!isAiLoginEnabled(ai) || isAiInLoading(ai)\"\r\n\t\t\t\t\t\t\t\t\t\t@change=\"toggleAI(ai, $event)\" color=\"#409EFF\" style=\"transform: scale(0.8);\" />\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"ai-capabilities\" v-if=\"ai.capabilities.length > 0\">\r\n\t\t\t\t\t\t\t\t<view v-for=\"capability in ai.capabilities\" :key=\"capability.value\"\r\n\t\t\t\t\t\t\t\t\tclass=\"capability-tag\"\r\n\t\t\t\t\t\t\t\t\t:class=\"[ai.selectedCapabilities.includes(capability.value) ? 'capability-active' : '', (!ai.enabled || !isAiLoginEnabled(ai)) ? 'capability-disabled' : '']\"\r\n\t\t\t\t\t\t\t\t\t@tap=\"toggleCapability(ai, capability.value)\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"capability-text\">{{ capability.label }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 提示词输入区块 -->\r\n\t\t\t<view class=\"section-block\" id=\"prompt-input\">\r\n\t\t\t\t<view class=\"section-header\" @tap=\"toggleSection('promptInput')\">\r\n\t\t\t\t\t<text class=\"section-title\">提示词输入</text>\r\n\t\t\t\t\t<text class=\"section-arrow\">\r\n\t\t\t\t\t\t{{ sectionExpanded.promptInput ? '▼' : '▶' }}\r\n\t\t\t\t\t</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"section-content\" v-if=\"sectionExpanded.promptInput\">\r\n\t\t\t\t\t<textarea class=\"prompt-textarea\" v-model=\"promptInput\" placeholder=\"请输入提示词\" maxlength=\"2000\"\r\n\t\t\t\t\t\tshow-confirm-bar=\"false\" auto-height></textarea>\r\n\t\t\t\t\t<view class=\"prompt-footer\">\r\n\t\t\t\t\t\t<text class=\"word-count\">{{ promptInput.length }}/2000</text>\r\n\t\t\t\t\t\t<button class=\"send-btn\" :class=\"[!canSend ? 'send-btn-disabled' : '']\" :disabled=\"!canSend\"\r\n\t\t\t\t\t\t\t@tap=\"sendPrompt\">\r\n\t\t\t\t\t\t\t发送\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 执行状态区块 -->\r\n\t\t\t<view class=\"section-block\" v-if=\"taskStarted\" id=\"task-status\">\r\n\t\t\t\t<view class=\"section-header\" @tap=\"toggleSection('taskStatus')\">\r\n\t\t\t\t\t<text class=\"section-title\">任务执行状态</text>\r\n\t\t\t\t\t<text class=\"section-arrow\">\r\n\t\t\t\t\t\t{{ sectionExpanded.taskStatus ? '▼' : '▶' }}\r\n\t\t\t\t\t</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"section-content\" v-if=\"sectionExpanded.taskStatus\">\r\n\t\t\t\t\t<!-- 任务流程 -->\r\n\t\t\t\t\t<view class=\"task-flow\">\r\n\t\t\t\t\t\t<view v-for=\"(ai, index) in enabledAIs\" :key=\"index\" class=\"task-item\">\r\n\t\t\t\t\t\t\t<view class=\"task-header\" @tap=\"toggleTaskExpansion(ai)\">\r\n\t\t\t\t\t\t\t\t<view class=\"task-left\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"task-arrow\">\r\n\t\t\t\t\t\t\t\t\t\t{{ ai.isExpanded ? '▼' : '▶' }}\r\n\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t\t<image class=\"task-avatar\" :src=\"ai.avatar\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t\t\t<text class=\"task-name\">{{ ai.name }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"task-right\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"status-text\">{{ getStatusText(ai.status) }}</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"status-icon\" :class=\"[getStatusIconClass(ai.status)]\">\r\n\t\t\t\t\t\t\t\t\t\t{{ getStatusEmoji(ai.status) }}\r\n\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 进度日志 -->\r\n\t\t\t\t\t\t\t<view class=\"progress-logs\" v-if=\"ai.isExpanded && ai.progressLogs.length > 0\">\r\n\t\t\t\t\t\t\t\t<view v-for=\"(log, logIndex) in ai.progressLogs\" :key=\"logIndex\" class=\"progress-item\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"progress-dot\" :class=\"[log.isCompleted ? 'dot-completed' : '']\"></view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"progress-content\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"progress-time\">{{ formatTime(log.timestamp) }}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"progress-text\">{{ log.content }}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 主机可视化 -->\r\n\t\t\t\t\t<!-- \t<view class=\"screenshots-section\" v-if=\"screenshots.length > 0\">\r\n\t\t\t\t\t\t<view class=\"screenshots-header\">\r\n\t\t\t\t\t\t\t<text class=\"section-subtitle\">主机可视化</text>\r\n\t\t\t\t\t\t\t<switch :checked=\"autoPlay\" @change=\"toggleAutoPlay\" color=\"#409EFF\"\r\n\t\t\t\t\t\t\t\tstyle=\"transform: scale(0.8);\" />\r\n\t\t\t\t\t\t\t<text class=\"auto-play-text\">自动轮播</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<swiper class=\"screenshots-swiper\" :autoplay=\"autoPlay\" :interval=\"3000\" :duration=\"500\"\r\n\t\t\t\t\t\t\tindicator-dots indicator-color=\"rgba(255,255,255,0.5)\" indicator-active-color=\"#409EFF\">\r\n\t\t\t\t\t\t\t<swiper-item v-for=\"(screenshot, index) in screenshots\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<image class=\"screenshot-image\" :src=\"screenshot\" mode=\"aspectFit\"\r\n\t\t\t\t\t\t\t\t\t@tap=\"previewImage(screenshot)\"></image>\r\n\t\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\t</swiper>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 结果展示区块 -->\r\n\t\t\t<view class=\"section-block\" v-if=\"results.length > 0\" id=\"results\">\r\n\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t<text class=\"section-title\">执行结果</text>\r\n\t\t\t\t\t<button class=\"score-btn\" size=\"mini\" @tap=\"showScoreModal\">智能评分</button>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"section-content\">\r\n\t\t\t\t\t<!-- 结果选项卡 -->\r\n\t\t\t\t\t<scroll-view class=\"result-tabs\" scroll-x>\r\n\t\t\t\t\t\t<view class=\"tab-container\">\r\n\t\t\t\t\t\t\t<view v-for=\"(result, index) in results\" :key=\"index\" class=\"result-tab\"\r\n\t\t\t\t\t\t\t\t:class=\"[activeResultIndex === index ? 'tab-active' : '']\"\r\n\t\t\t\t\t\t\t\t@tap=\"switchResultTab(index)\">\r\n\t\t\t\t\t\t\t\t<text class=\"tab-text\">{{ result.aiName }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</scroll-view>\r\n\r\n\t\t\t\t\t<!-- 结果内容 -->\r\n\t\t\t\t\t<view class=\"result-content\" v-if=\"currentResult\">\r\n\t\t\t\t\t\t<!-- 结果标题 -->\r\n\t\t\t\t\t\t<!-- <view class=\"result-header\">\r\n\t\t\t\t\t\t\t<text class=\"result-title\">{{ currentResult.aiName }}的执行结果</text>\r\n\t\t\t\t\t\t</view> -->\r\n\r\n\t\t\t\t\t\t<!-- 操作按钮 -->\r\n\t\t\t\t\t\t<view class=\"result-actions\">\r\n\t\t\t\t\t\t\t<button class=\"share-link-btn\" size=\"mini\" v-if=\"currentResult.shareUrl\"\r\n\t\t\t\t\t\t\t\t@tap=\"openShareUrl(currentResult.shareUrl)\">\r\n\t\t\t\t\t\t\t\t复制原链接\r\n\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t<button class=\"action-btn-small\" size=\"mini\"\r\n\t\t\t\t\t\t\t\t@tap=\"copyResult(currentResult.content)\">复制(纯文本)</button>\r\n\t\t\t\t\t\t\t<button class=\"collect-btn\" size=\"mini\"\r\n\t\t\t\t\t\t\t\t@tap=\"collectToOffice(currentResult.content)\">投递到公众号</button>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<!-- 分享图片或内容 -->\r\n\t\t\t\t\t\t<view class=\"result-body\">\r\n\t\t\t\t\t\t\t<!-- 图片内容 -->\r\n\t\t\t\t\t\t\t<view v-if=\"currentResult.shareImgUrl && isImageFile(currentResult.shareImgUrl)\"\r\n\t\t\t\t\t\t\t\tclass=\"result-image-container\">\r\n\t\t\t\t\t\t\t\t<image class=\"result-image\" :src=\"currentResult.shareImgUrl\" mode=\"widthFix\"\r\n\t\t\t\t\t\t\t\t\t@tap=\"previewImage(currentResult.shareImgUrl)\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- PDF文件内容 -->\r\n\t\t\t\t\t\t\t<view v-else-if=\"currentResult.shareImgUrl && isPdfFile(currentResult.shareImgUrl)\"\r\n\t\t\t\t\t\t\t\tclass=\"result-pdf-container\">\r\n\t\t\t\t\t\t\t\t<view class=\"pdf-placeholder\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"pdf-icon\">📄</view>\r\n\t\t\t\t\t\t\t\t\t<text class=\"pdf-text\">PDF文件</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"pdf-actions\">\r\n\t\t\t\t\t\t\t\t\t\t<button class=\"pdf-btn download-btn\" size=\"mini\"\r\n\t\t\t\t\t\t\t\t\t\t\t@tap=\"openPdfFile(currentResult.shareImgUrl)\">\r\n\t\t\t\t\t\t\t\t\t\t\t打开文件\r\n\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t<button class=\"pdf-btn copy-btn\" size=\"mini\"\r\n\t\t\t\t\t\t\t\t\t\t\t@tap=\"copyPdfUrl(currentResult.shareImgUrl)\">\r\n\t\t\t\t\t\t\t\t\t\t\t复制链接\r\n\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 文字内容 -->\r\n\t\t\t\t\t\t\t<view v-else class=\"result-text\">\r\n\t\t\t\t\t\t\t\t<rich-text :nodes=\"renderMarkdown(currentResult.content)\"></rich-text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\r\n\t\t<!-- 历史记录抽屉 -->\r\n\t\t<view v-if=\"historyDrawerVisible\" class=\"drawer-mask\" @tap=\"closeHistoryDrawer\">\r\n\t\t\t<view class=\"drawer-container\" @tap.stop>\r\n\t\t\t\t<view class=\"drawer-content\">\r\n\t\t\t\t\t<view class=\"drawer-header\">\r\n\t\t\t\t\t\t<text class=\"drawer-title\">历史会话记录</text>\r\n\t\t\t\t\t\t<text class=\"close-icon\" @tap=\"closeHistoryDrawer\">✕</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<scroll-view class=\"history-list\" scroll-y>\r\n\t\t\t\t\t\t<view v-for=\"(group, date) in groupedHistory\" :key=\"date\" class=\"history-group\">\r\n\t\t\t\t\t\t\t<text class=\"history-date\">{{ date }}</text>\r\n\t\t\t\t\t\t\t<view v-for=\"(item, index) in group\" :key=\"index\" class=\"history-item\"\r\n\t\t\t\t\t\t\t\t@tap=\"loadHistoryItem(item)\">\r\n\t\t\t\t\t\t\t\t<text class=\"history-prompt\">{{ item.userPrompt }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"history-time\">{{ formatHistoryTime(item.createTime) }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 智能评分弹窗 -->\r\n\t\t<view v-if=\"scoreModalVisible\" class=\"popup-mask\" @tap=\"closeScoreModal\">\r\n\t\t\t<view class=\"score-modal\" @tap.stop>\r\n\t\t\t\t<view class=\"score-header\">\r\n\t\t\t\t\t<text class=\"score-title\">智能评分</text>\r\n\t\t\t\t\t<text class=\"close-icon\" @tap=\"closeScoreModal\">✕</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"score-content\">\r\n\t\t\t\t\t<view class=\"score-prompt-section\">\r\n\t\t\t\t\t\t<text class=\"score-subtitle\">评分提示词：</text>\r\n\t\t\t\t\t\t<textarea class=\"score-textarea\" v-model=\"scorePrompt\"\r\n\t\t\t\t\t\t\tplaceholder=\"请输入评分提示词，例如：请从内容质量、逻辑性、创新性等方面进行评分\" maxlength=\"1000\"></textarea>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"score-selection\">\r\n\t\t\t\t\t\t<text class=\"score-subtitle\">选择要评分的内容：</text>\r\n\t\t\t\t\t\t<checkbox-group @change=\"toggleResultSelection\">\r\n\t\t\t\t\t\t\t<view class=\"score-checkboxes\">\r\n\t\t\t\t\t\t\t\t<label v-for=\"(result, index) in results\" :key=\"index\" class=\"checkbox-item\">\r\n\t\t\t\t\t\t\t\t\t<checkbox :value=\"result.aiName\"\r\n\t\t\t\t\t\t\t\t\t\t:checked=\"selectedResults.includes(result.aiName)\" />\r\n\t\t\t\t\t\t\t\t\t<text class=\"checkbox-text\">{{ result.aiName }}</text>\r\n\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<button class=\"score-submit-btn\" :disabled=\"!canScore\" @tap=\"handleScore\">\r\n\t\t\t\t\t\t开始评分\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmarked\r\n\t} from 'marked';\r\n\timport {\r\n\t\tmessage, saveUserChatData, getChatHistory,pushAutoOffice\r\n\t} from \"@/api/wechat/aigc\";\r\n\timport {\r\n\t\tv4 as uuidv4\r\n\t} from 'uuid';\r\n\timport storage from '@/utils/storage'\r\n\timport constant from '@/utils/constant'\r\n\r\n\r\n\texport default {\r\n\t\tname: 'MiniConsole',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 用户信息\r\n\t\t\t\tuserId: '',\r\n\t\t\t\tcorpId: '',\r\n\t\t\t\tchatId: '',\r\n\t\t\t\texpandedHistoryItems: {},\r\n\t\t\t\tuserInfoReq: {\r\n\t\t\t\t\tuserPrompt: '',\r\n\t\t\t\t\tuserId: '',\r\n\t\t\t\t\tcorpId: '',\r\n\t\t\t\t\ttaskId: '',\r\n\t\t\t\t\troles: '',\r\n\t\t\t\t\ttoneChatId: '',\r\n\t\t\t\t\tybDsChatId: '',\r\n\t\t\t\t\tdbChatId: '',\r\n\t\t\t\t\tisNewChat: true\r\n\t\t\t\t},\r\n\t\t\t\tjsonRpcReqest: {\r\n\t\t\t\t\tjsonrpc: '2.0',\r\n\t\t\t\t\tid: '',\r\n\t\t\t\t\tmethod: '',\r\n\t\t\t\t\tparams: {}\r\n\t\t\t\t},\r\n\r\n\t\t\t\t// 区域展开状态\r\n\t\t\t\tsectionExpanded: {\r\n\t\t\t\t\taiConfig: true,\r\n\t\t\t\t\tpromptInput: true,\r\n\t\t\t\t\ttaskStatus: true\r\n\t\t\t\t},\r\n\r\n\t\t\t\t// AI配置（参考PC端完整配置）\r\n\t\t\t\taiList: [{\r\n\t\t\t\t\t\tname: 'TurboS@元器',\r\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/yuanbao.png',\r\n\t\t\t\t\t\tcapabilities: [],\r\n\t\t\t\t\t\tselectedCapabilities: [],\r\n\t\t\t\t\t\tenabled: true,\r\n\t\t\t\t\t\tstatus: 'idle',\r\n\t\t\t\t\t\tprogressLogs: [],\r\n\t\t\t\t\t\tisExpanded: true\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: 'TurboS长文版@元器',\r\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/yuanbao.png',\r\n\t\t\t\t\t\tcapabilities: [],\r\n\t\t\t\t\t\tselectedCapabilities: [],\r\n\t\t\t\t\t\tenabled: true,\r\n\t\t\t\t\t\tstatus: 'idle',\r\n\t\t\t\t\t\tprogressLogs: [],\r\n\t\t\t\t\t\tisExpanded: true\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '腾讯元宝T1',\r\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/yuanbao.png',\r\n\t\t\t\t\t\tcapabilities: [{\r\n\t\t\t\t\t\t\t\tlabel: '深度思考',\r\n\t\t\t\t\t\t\t\tvalue: 'deep_thinking'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tlabel: '联网搜索',\r\n\t\t\t\t\t\t\t\tvalue: 'web_search'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t],\r\n\t\t\t\t\t\tselectedCapabilities: ['deep_thinking', 'web_search'],\r\n\t\t\t\t\t\tenabled: true,\r\n\t\t\t\t\t\tstatus: 'idle',\r\n\t\t\t\t\t\tprogressLogs: [],\r\n\t\t\t\t\t\tisExpanded: true\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '腾讯元宝DS',\r\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/yuanbao.png',\r\n\t\t\t\t\t\tcapabilities: [{\r\n\t\t\t\t\t\t\t\tlabel: '深度思考',\r\n\t\t\t\t\t\t\t\tvalue: 'deep_thinking'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tlabel: '联网搜索',\r\n\t\t\t\t\t\t\t\tvalue: 'web_search'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t],\r\n\t\t\t\t\t\tselectedCapabilities: ['deep_thinking', 'web_search'],\r\n\t\t\t\t\t\tenabled: true,\r\n\t\t\t\t\t\tstatus: 'idle',\r\n\t\t\t\t\t\tprogressLogs: [],\r\n\t\t\t\t\t\tisExpanded: true\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '豆包',\r\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/%E8%B1%86%E5%8C%85.png',\r\n\t\t\t\t\t\tcapabilities: [{\r\n\t\t\t\t\t\t\tlabel: '深度思考',\r\n\t\t\t\t\t\t\tvalue: 'deep_thinking'\r\n\t\t\t\t\t\t}],\r\n\t\t\t\t\t\tselectedCapabilities: ['deep_thinking'],\r\n\t\t\t\t\t\tenabled: true,\r\n\t\t\t\t\t\tstatus: 'idle',\r\n\t\t\t\t\t\tprogressLogs: [],\r\n\t\t\t\t\t\tisExpanded: true\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\r\n\t\t\t\t// 输入和任务状态\r\n\t\t\t\tpromptInput: '',\r\n\t\t\t\ttaskStarted: false,\r\n\t\t\t\tenabledAIs: [],\r\n\r\n\t\t\t\t// 可视化\r\n\t\t\t\tscreenshots: [],\r\n\t\t\t\tautoPlay: false,\r\n\r\n\t\t\t\t// 结果\r\n\t\t\t\tresults: [],\r\n\t\t\t\tactiveResultIndex: 0,\r\n\r\n\t\t\t\t// 历史记录\r\n\t\t\t\tchatHistory: [],\r\n\r\n\t\t\t\t// 评分\r\n\t\t\t\tselectedResults: [],\r\n\t\t\t\tscorePrompt: '请你深度阅读以下几篇公众号文章，从多个维度进行逐项打分，输出评分结果。并在以下各篇文章的基础上博采众长，综合整理一篇更全面的文章。',\r\n\r\n\t\t\t\t// 收录计数器\r\n\t\t\t\tcollectNum: 0,\r\n\r\n\t\t\t\t// WebSocket\r\n\t\t\t\tsocketTask: null,\r\n\t\t\t\treconnectTimer: null,\r\n\t\t\t\theartbeatTimer: null,\r\n\t\t\t\treconnectCount: 0,\r\n\t\t\t\tmaxReconnectCount: 5,\r\n\t\t\t\tisConnecting: false,\r\n\t\t\t\tshouldReconnect: true, // 控制是否应该重连\r\n\t\t\t\tscrollIntoView: '',\r\n\r\n\t\t\t\t// 弹窗状态\r\n\t\t\t\thistoryDrawerVisible: false,\r\n\t\t\t\tscoreModalVisible: false,\r\n\r\n\t\t\t\t// AI登录状态\r\n\t\t\t\taiLoginStatus: {\r\n\t\t\t\t\tyuanbao: false,\r\n\t\t\t\t\tdoubao: false,\r\n\t\t\t\t\tagent: false\r\n\t\t\t\t},\r\n\t\t\t\taccounts: {\r\n\t\t\t\t\tyuanbao: '',\r\n\t\t\t\t\tdoubao: '',\r\n\t\t\t\t\tagent: ''\r\n\t\t\t\t},\r\n\t\t\t\tisLoading: {\r\n\t\t\t\t\tyuanbao: true,\r\n\t\t\t\t\tdoubao: true,\r\n\t\t\t\t\tagent: true\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tcomputed: {\r\n\t\t\tcanSend() {\r\n\t\t\t\t// 检查是否有输入内容\r\n\t\t\t\tconst hasInput = this.promptInput.trim().length > 0;\r\n\r\n\t\t\t\t// 检查是否有可用的AI（既启用又已登录）\r\n\t\t\t\tconst hasAvailableAI = this.aiList.some(ai => ai.enabled && this.isAiLoginEnabled(ai));\r\n\r\n\t\t\t\t// 检查是否正在加载AI状态（如果正在加载，禁用发送按钮）\r\n\t\t\t\tconst isCheckingStatus = this.isLoading.yuanbao || this.isLoading.doubao || this.isLoading.agent;\r\n\r\n\t\t\t\treturn hasInput && hasAvailableAI && !isCheckingStatus;\r\n\t\t\t},\r\n\r\n\t\t\tcanScore() {\r\n\t\t\t\tconst hasSelected = this.selectedResults.length > 0;\r\n\t\t\t\tconst hasPrompt = this.scorePrompt.trim().length > 0;\r\n\t\t\t\tconsole.log('canScore - selectedResults:', this.selectedResults);\r\n\t\t\t\tconsole.log('canScore - scorePrompt length:', this.scorePrompt.trim().length);\r\n\t\t\t\tconsole.log('canScore - hasSelected:', hasSelected, 'hasPrompt:', hasPrompt);\r\n\t\t\t\treturn hasSelected && hasPrompt;\r\n\t\t\t},\r\n\r\n\t\t\tcurrentResult() {\r\n\t\t\t\treturn this.results[this.activeResultIndex] || null;\r\n\t\t\t},\r\n\r\n\t\t\tgroupedHistory() {\r\n\t\t\t\tconst groups = {};\r\n\t\t\t\tconst chatGroups = {};\r\n\r\n\t\t\t\t// 首先按chatId分组\r\n\t\t\t\tthis.chatHistory.forEach(item => {\r\n\t\t\t\t\tif (!chatGroups[item.chatId]) {\r\n\t\t\t\t\t\tchatGroups[item.chatId] = [];\r\n\t\t\t\t\t}\r\n\t\t\t\t\tchatGroups[item.chatId].push(item);\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 然后按日期分组，并处理父子关系\r\n\t\t\t\tObject.values(chatGroups).forEach(chatGroup => {\r\n\t\t\t\t\t// 按时间排序\r\n\t\t\t\t\tchatGroup.sort((a, b) => new Date(a.createTime) - new Date(b.createTime));\r\n\r\n\t\t\t\t\t// 获取最早的记录作为父级\r\n\t\t\t\t\tconst parentItem = chatGroup[0];\r\n\t\t\t\t\tconst date = this.getHistoryDate(parentItem.createTime);\r\n\r\n\t\t\t\t\tif (!groups[date]) {\r\n\t\t\t\t\t\tgroups[date] = [];\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 添加父级记录\r\n\t\t\t\t\tgroups[date].push({\r\n\t\t\t\t\t\t...parentItem,\r\n\t\t\t\t\t\tisParent: true,\r\n\t\t\t\t\t\tisExpanded: this.expandedHistoryItems[parentItem.chatId] || false,\r\n\t\t\t\t\t\tchildren: chatGroup.slice(1).map(child => ({\r\n\t\t\t\t\t\t\t...child,\r\n\t\t\t\t\t\t\tisParent: false\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\r\n\t\t\t\treturn groups;\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.initUserInfo();\r\n\r\n\t\t\t// 检查用户信息是否完整\r\n\t\t\tif (!this.userId) {\r\n\t\t\t\tconsole.log('用户ID不存在，跳转到登录页面');\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: '请先登录后再使用',\r\n\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\tconfirmText: '去登录',\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/pages/login/index'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tthis.initWebSocket();\r\n\t\t\tthis.loadChatHistory(0); // 加载历史记录\r\n\t\t\tthis.loadLastChat(); // 加载上次会话\r\n\t\t\tthis.checkAiLoginStatus(); // 检查AI登录状态\r\n\t\t},\r\n\r\n\t\tonUnload() {\r\n\t\t\tthis.closeWebSocket();\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t// 初始化用户信息\r\n\t\t\tinitUserInfo() {\r\n\t\t\t\t// 从store获取用户信息，兼容缓存方式\r\n\t\t\tthis.userId = storage.get(constant.userId);\r\n\t\t\tthis.corpId = storage.get(constant.corpId);\r\n\r\n\t\t\t\tthis.chatId = this.generateUUID();\r\n\r\n\t\t\t\t// 初始化请求参数\r\n\t\t\t\tthis.userInfoReq.userId = this.userId;\r\n\t\t\t\tthis.userInfoReq.corpId = this.corpId;\r\n\r\n\t\t\t\tconsole.log('初始化用户信息:', {\r\n\t\t\t\t\tuserId: this.userId,\r\n\t\t\t\t\tcorpId: this.corpId\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 生成UUID\r\n\t\t\tgenerateUUID() {\r\n\t\t\t\treturn uuidv4();\r\n\t\t\t},\r\n\r\n\t\t\t// 切换区域展开状态\r\n\t\t\ttoggleSection(section) {\r\n\t\t\t\tthis.sectionExpanded[section] = !this.sectionExpanded[section];\r\n\t\t\t},\r\n\r\n\t\t\t// 切换AI启用状态\r\n\t\t\ttoggleAI(ai, event) {\r\n\t\t\t\t// 检查AI是否已登录\r\n\t\t\t\tif (!this.isAiLoginEnabled(ai)) {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: `${ai.name}需要先登录，请前往PC端进行登录后再使用`,\r\n\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\tconfirmText: '知道了'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tai.enabled = event.detail.value;\r\n\t\t\t},\r\n\r\n\t\t\t// 切换AI能力\r\n\t\t\ttoggleCapability(ai, capabilityValue) {\r\n\t\t\t\t// 检查AI是否已登录和启用\r\n\t\t\t\tif (!this.isAiLoginEnabled(ai)) {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: `${ai.name}需要先登录，请前往PC端进行登录后再使用`,\r\n\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\tconfirmText: '知道了'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!ai.enabled) return;\r\n\r\n\t\t\t\tconst index = ai.selectedCapabilities.indexOf(capabilityValue);\r\n\t\t\t\tif (index === -1) {\r\n\t\t\t\t\tai.selectedCapabilities.push(capabilityValue);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tai.selectedCapabilities.splice(index, 1);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 发送提示词\r\n\t\t\tsendPrompt() {\r\n\t\t\t\tif (!this.canSend) return;\r\n\r\n\t\t\t\tthis.screenshots = [];\r\n\t\t\t\tconsole.log('清空截图数组');\r\n\t\t\t\t// 折叠所有区域\r\n\t\t\t\tthis.sectionExpanded.aiConfig = false;\r\n\t\t\t\tthis.sectionExpanded.promptInput = false;\r\n\t\t\t\t// this.sectionExpanded.taskStatus = false;\r\n\r\n\t\t\t\tthis.taskStarted = true;\r\n\t\t\t\tthis.results = []; // 清空之前的结果\r\n\r\n\t\t\t\tthis.userInfoReq.roles = '';\r\n\t\t\t\tthis.userInfoReq.taskId = this.generateUUID();\r\n\t\t\t\tthis.userInfoReq.userId = this.userId;\r\n\t\t\t\tthis.userInfoReq.corpId = this.corpId;\r\n\t\t\t\tthis.userInfoReq.userPrompt = this.promptInput;\r\n\r\n\t\t\t\t// 获取启用的AI列表及其状态\r\n\t\t\t\tthis.enabledAIs = this.aiList.filter(ai => ai.enabled);\r\n\r\n\t\t\t\t// 将所有启用的AI状态设置为运行中\r\n\t\t\t\tthis.enabledAIs.forEach(ai => {\r\n\t\t\t\t\tai.status = 'running';\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 构建角色参数\r\n\t\t\t\tthis.enabledAIs.forEach(ai => {\r\n\t\t\t\t\tif (ai.name === '腾讯元宝T1') {\r\n\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-pt,';\r\n\t\t\t\t\t\tif (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n\t\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-sdsk,';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (ai.selectedCapabilities.includes(\"web_search\")) {\r\n\t\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-lwss,';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (ai.name === '腾讯元宝DS') {\r\n\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-pt,';\r\n\t\t\t\t\t\tif (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n\t\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-sdsk,';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (ai.selectedCapabilities.includes(\"web_search\")) {\r\n\t\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-lwss,';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (ai.name === 'TurboS@元器') {\r\n\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'cube-trubos-agent,';\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (ai.name === 'TurboS长文版@元器') {\r\n\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'cube-turbos-large-agent,';\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (ai.name === '豆包') {\r\n\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'zj-db,';\r\n\t\t\t\t\t\tif (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n\t\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'zj-db-sdsk,';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\r\n\t\t\t\tconsole.log(\"参数：\", this.userInfoReq);\r\n\r\n\t\t\t\t// 滚动到任务状态区域\r\n\t\t\t\tthis.scrollIntoView = 'task-status';\r\n\r\n\t\t\t\t//调用后端接口\r\n\t\t\t\tthis.jsonRpcReqest.id = this.generateUUID();\r\n\t\t\t\tthis.jsonRpcReqest.method = \"使用F8S\";\r\n\t\t\t\tthis.jsonRpcReqest.params = this.userInfoReq;\r\n\t\t\t\tthis.message(this.jsonRpcReqest);\r\n\t\t\t\tthis.userInfoReq.isNewChat = false;\r\n\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '任务已提交',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\r\n\r\n\t\t\t\t\t// WebSocket相关方法\r\n\t\tinitWebSocket() {\r\n\t\t\t// 检查用户信息是否完整\r\n\t\t\tif (!this.userId) {\r\n\t\t\t\tconsole.log('用户ID不存在，跳转到登录页面');\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: '请先登录后再使用',\r\n\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\tconfirmText: '去登录',\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/pages/login/index'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tif (this.isConnecting) {\r\n\t\t\t\tconsole.log('WebSocket正在连接中，跳过重复连接');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 先关闭现有连接，避免连接泄漏\r\n\t\t\tif (this.socketTask) {\r\n\t\t\t\tconsole.log('关闭现有WebSocket连接');\r\n\t\t\t\tthis.socketTask.close();\r\n\t\t\t\tthis.socketTask = null;\r\n\t\t\t}\r\n\r\n\t\t\tthis.isConnecting = true;\r\n\t\t\tthis.shouldReconnect = true; // 允许重连\r\n\r\n\t\t\t// 使用PC端的WebSocket连接方式\r\n\t\t\tconst wsUrl = `${process.env.VUE_APP_WS_API || 'ws://127.0.0.1:8081/websocket?clientId=mypc-'}${this.userId}`;\r\n\t\t\tconsole.log('WebSocket URL:', wsUrl);\r\n\r\n\t\t\tthis.socketTask = uni.connectSocket({\r\n\t\t\t\turl: wsUrl,\r\n\t\t\t\tsuccess: () => {\r\n\t\t\t\t\tconsole.log('WebSocket连接成功');\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\tconsole.error('WebSocket连接失败', err);\r\n\t\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\t\tthis.handleReconnect();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\tthis.socketTask.onOpen(() => {\r\n\t\t\t\tconsole.log('WebSocket连接已打开');\r\n\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\tthis.reconnectCount = 0; // 重置重连次数\r\n\r\n\t\t\t\t// 清理重连定时器\r\n\t\t\t\tif (this.reconnectTimer) {\r\n\t\t\t\t\tclearTimeout(this.reconnectTimer);\r\n\t\t\t\t\tthis.reconnectTimer = null;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '连接成功',\r\n\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\tduration: 1000\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 开始心跳检测\r\n\t\t\t\tthis.startHeartbeat();\r\n\t\t\t});\r\n\r\n\t\t\tthis.socketTask.onMessage((res) => {\r\n\t\t\t\tthis.handleWebSocketMessage(res.data);\r\n\t\t\t});\r\n\r\n\t\t\tthis.socketTask.onError((err) => {\r\n\t\t\t\tconsole.error('WebSocket连接错误', err);\r\n\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: 'WebSocket连接错误',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\tthis.handleReconnect();\r\n\t\t\t});\r\n\r\n\t\t\tthis.socketTask.onClose(() => {\r\n\t\t\t\tconsole.log('WebSocket连接已关闭');\r\n\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\tthis.stopHeartbeat(); // 停止心跳\r\n\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: 'WebSocket连接已关闭',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 只有在需要重连时才重连\r\n\t\t\t\tif (this.shouldReconnect) {\r\n\t\t\t\t\tthis.handleReconnect();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 处理重连\r\n\t\thandleReconnect() {\r\n\t\t\tif (this.reconnectCount >= this.maxReconnectCount) {\r\n\t\t\t\tconsole.log('WebSocket重连次数已达上限');\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '连接失败',\r\n\t\t\t\t\tcontent: '网络连接不稳定，请检查网络后手动刷新页面',\r\n\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\tconfirmText: '知道了'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 清理现有的重连定时器\r\n\t\t\tif (this.reconnectTimer) {\r\n\t\t\t\tclearTimeout(this.reconnectTimer);\r\n\t\t\t\tthis.reconnectTimer = null;\r\n\t\t\t}\r\n\r\n\t\t\tthis.reconnectCount++;\r\n\t\t\tconst delay = Math.min(1000 * Math.pow(2, this.reconnectCount), 30000); // 指数退避，最大30秒\r\n\r\n\t\t\tconsole.log(`WebSocket将在${delay}ms后进行第${this.reconnectCount}次重连`);\r\n\r\n\t\t\tthis.reconnectTimer = setTimeout(() => {\r\n\t\t\t\tconsole.log(`开始第${this.reconnectCount}次重连`);\r\n\t\t\t\tthis.initWebSocket();\r\n\t\t\t}, delay);\r\n\t\t},\r\n\r\n\t\t// 开始心跳检测\r\n\t\tstartHeartbeat() {\r\n\t\t\tthis.stopHeartbeat(); // 先停止之前的心跳\r\n\r\n\t\t\tthis.heartbeatTimer = setInterval(() => {\r\n\t\t\t\tif (this.socketTask) {\r\n\t\t\t\t\tthis.sendWebSocketMessage({\r\n\t\t\t\t\t\ttype: 'HEARTBEAT',\r\n\t\t\t\t\t\ttimestamp: Date.now()\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}, 30000); // 每30秒发送一次心跳\r\n\t\t},\r\n\r\n\t\t// 停止心跳检测\r\n\t\tstopHeartbeat() {\r\n\t\t\tif (this.heartbeatTimer) {\r\n\t\t\t\tclearInterval(this.heartbeatTimer);\r\n\t\t\t\tthis.heartbeatTimer = null;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t\tsendWebSocketMessage(data) {\r\n\t\t\t\tif (this.socketTask) {\r\n\t\t\t\t\tthis.socketTask.send({\r\n\t\t\t\t\t\tdata: JSON.stringify(data)\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.warn('WebSocket未连接，无法发送消息');\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 调用后端message接口\r\n\t\t\tmessage(data) {\r\n\t\t\t\tmessage(data).then(res => {\r\n\t\t\t\t\tif (res.code == 201) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.messages,\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t\t\tcloseWebSocket() {\r\n\t\t\t// 设置不需要重连\r\n\t\t\tthis.shouldReconnect = false;\r\n\r\n\t\t\t// 清理重连定时器\r\n\t\t\tif (this.reconnectTimer) {\r\n\t\t\t\tclearTimeout(this.reconnectTimer);\r\n\t\t\t\tthis.reconnectTimer = null;\r\n\t\t\t}\r\n\r\n\t\t\t// 停止心跳检测\r\n\t\t\tthis.stopHeartbeat();\r\n\r\n\t\t\t// 关闭WebSocket连接\r\n\t\t\tif (this.socketTask) {\r\n\t\t\t\tthis.socketTask.close();\r\n\t\t\t\tthis.socketTask = null;\r\n\t\t\t}\r\n\r\n\t\t\t// 重置状态\r\n\t\t\tthis.isConnecting = false;\r\n\t\t\tthis.reconnectCount = 0;\r\n\t\t},\r\n\r\n\t\t\t\t\t// 处理WebSocket消息\r\n\t\thandleWebSocketMessage(data) {\r\n\t\t\ttry {\r\n\t\t\t\tconst datastr = data;\r\n\t\t\t\tconst dataObj = JSON.parse(datastr);\r\n\r\n\t\t\t\t// 忽略心跳响应\r\n\t\t\t\tif (dataObj.type === 'HEARTBEAT_RESPONSE' || dataObj.type === 'HEARTBEAT') {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t\t// 处理chatId消息\r\n\t\t\t\t\tif (dataObj.type === 'RETURN_YBT1_CHATID' && dataObj.chatId) {\r\n\t\t\t\t\t\tthis.userInfoReq.toneChatId = dataObj.chatId;\r\n\t\t\t\t\t} else if (dataObj.type === 'RETURN_YBDS_CHATID' && dataObj.chatId) {\r\n\t\t\t\t\t\tthis.userInfoReq.ybDsChatId = dataObj.chatId;\r\n\t\t\t\t\t} else if (dataObj.type === 'RETURN_DB_CHATID' && dataObj.chatId) {\r\n\t\t\t\t\t\tthis.userInfoReq.dbChatId = dataObj.chatId;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 处理进度日志消息\r\n\t\t\t\t\tif (dataObj.type === 'RETURN_PC_TASK_LOG' && dataObj.aiName) {\r\n\t\t\t\t\t\tconst targetAI = this.enabledAIs.find(ai => ai.name === dataObj.aiName);\r\n\t\t\t\t\t\tif (targetAI) {\r\n\t\t\t\t\t\t\t// 将新进度添加到数组开头\r\n\t\t\t\t\t\t\ttargetAI.progressLogs.unshift({\r\n\t\t\t\t\t\t\t\tcontent: dataObj.content,\r\n\t\t\t\t\t\t\t\ttimestamp: new Date(),\r\n\t\t\t\t\t\t\t\tisCompleted: false\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 处理截图消息\r\n\t\t\t\t\tif (dataObj.type === 'RETURN_PC_TASK_IMG' && dataObj.url) {\r\n\t\t\t\t\t\tconsole.log('收到截图消息，原始URL:', dataObj.url);\r\n\t\t\t\t\t\t// 添加完整的URL前缀\r\n\t\t\t\t\t\tconst fullUrl = dataObj.url.startsWith('http') ? dataObj.url : `http://localhost${dataObj.url}`;\r\n\t\t\t\t\t\tconsole.log('处理后的URL:', fullUrl);\r\n\t\t\t\t\t\t// 将新的截图添加到数组开头\r\n\t\t\t\t\t\tthis.screenshots.unshift(fullUrl);\r\n\t\t\t\t\t\tconsole.log('当前screenshots数组:', this.screenshots);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 处理智能评分结果\r\n\t\t\t\t\tif (dataObj.type === 'RETURN_WKPF_RES') {\r\n\t\t\t\t\t\tconst wkpfAI = this.enabledAIs.find(ai => ai.name === '智能评分');\r\n\t\t\t\t\t\tif (wkpfAI) {\r\n\t\t\t\t\t\t\twkpfAI.status = 'completed';\r\n\t\t\t\t\t\t\tif (wkpfAI.progressLogs.length > 0) {\r\n\t\t\t\t\t\t\t\twkpfAI.progressLogs[0].isCompleted = true;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// 添加评分结果到results最前面\r\n\t\t\t\t\t\t\tthis.results.unshift({\r\n\t\t\t\t\t\t\t\taiName: '智能评分',\r\n\t\t\t\t\t\t\t\tcontent: dataObj.draftContent,\r\n\t\t\t\t\t\t\t\tshareUrl: dataObj.shareUrl || '',\r\n\t\t\t\t\t\t\t\tshareImgUrl: dataObj.shareImgUrl || '',\r\n\t\t\t\t\t\t\t\ttimestamp: new Date()\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tthis.activeResultIndex = 0;\r\n\r\n\t\t\t\t\t\t\t// 折叠所有区域当智能评分完成时\r\n\t\t\t\t\t\t\tthis.sectionExpanded.aiConfig = false;\r\n\t\t\t\t\t\t\tthis.sectionExpanded.promptInput = false;\r\n\t\t\t\t\t\t\tthis.sectionExpanded.taskStatus = false;\r\n\r\n\t\t\t\t\t\t\t// 智能评分完成时，再次保存历史记录\r\n\t\t\t\t\t\t\tthis.saveHistory();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 处理AI登录状态消息\r\n\t\t\t\t\tthis.handleAiStatusMessage(datastr, dataObj);\r\n\r\n\t\t\t\t\t// 处理AI结果\r\n\t\t\t\t\tthis.handleAIResult(dataObj);\r\n\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('WebSocket消息处理错误', error);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\thandleAiStatusMessage(datastr, dataObj) {\r\n\t\t\t\t// 处理腾讯元宝登录状态\r\n\t\t\t\tif (datastr.includes(\"RETURN_YB_STATUS\") && dataObj.status != '') {\r\n\t\t\t\t\tthis.isLoading.yuanbao = false;\r\n\t\t\t\t\tif (!datastr.includes(\"false\")) {\r\n\t\t\t\t\t\tthis.aiLoginStatus.yuanbao = true;\r\n\t\t\t\t\t\tthis.accounts.yuanbao = dataObj.status;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.aiLoginStatus.yuanbao = false;\r\n\t\t\t\t\t\t// 禁用相关AI\r\n\t\t\t\t\t\tthis.disableAIsByLoginStatus('yuanbao');\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 更新AI启用状态\r\n\t\t\t\t\tthis.updateAiEnabledStatus();\r\n\t\t\t\t}\r\n\t\t\t\t// 处理豆包登录状态\r\n\t\t\t\telse if (datastr.includes(\"RETURN_DB_STATUS\") && dataObj.status != '') {\r\n\t\t\t\t\tthis.isLoading.doubao = false;\r\n\t\t\t\t\tif (!datastr.includes(\"false\")) {\r\n\t\t\t\t\t\tthis.aiLoginStatus.doubao = true;\r\n\t\t\t\t\t\tthis.accounts.doubao = dataObj.status;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.aiLoginStatus.doubao = false;\r\n\t\t\t\t\t\t// 禁用相关AI\r\n\t\t\t\t\t\tthis.disableAIsByLoginStatus('doubao');\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 更新AI启用状态\r\n\t\t\t\t\tthis.updateAiEnabledStatus();\r\n\t\t\t\t}\r\n\t\t\t\t// 处理智能体登录状态\r\n\t\t\t\telse if (datastr.includes(\"RETURN_AGENT_STATUS\") && dataObj.status != '') {\r\n\t\t\t\t\tthis.isLoading.agent = false;\r\n\t\t\t\t\tif (!datastr.includes(\"false\")) {\r\n\t\t\t\t\t\tthis.aiLoginStatus.agent = true;\r\n\t\t\t\t\t\tthis.accounts.agent = dataObj.status;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.aiLoginStatus.agent = false;\r\n\t\t\t\t\t\t// 禁用相关AI\r\n\t\t\t\t\t\tthis.disableAIsByLoginStatus('agent');\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 更新AI启用状态\r\n\t\t\t\t\tthis.updateAiEnabledStatus();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\thandleAIResult(dataObj) {\r\n\t\t\t\tlet targetAI = null;\r\n\r\n\t\t\t\t// 根据消息类型匹配AI\r\n\t\t\t\tswitch (dataObj.type) {\r\n\t\t\t\t\tcase 'RETURN_YBT1_RES':\r\n\t\t\t\t\t\tconsole.log('收到消息:', dataObj);\r\n\t\t\t\t\t\ttargetAI = this.enabledAIs.find(ai => ai.name === '腾讯元宝T1');\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'RETURN_YBDS_RES':\r\n\t\t\t\t\t\tconsole.log('收到消息:', dataObj);\r\n\t\t\t\t\t\ttargetAI = this.enabledAIs.find(ai => ai.name === '腾讯元宝DS');\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'RETURN_DB_RES':\r\n\t\t\t\t\t\tconsole.log('收到消息:', dataObj);\r\n\t\t\t\t\t\ttargetAI = this.enabledAIs.find(ai => ai.name === '豆包');\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'RETURN_TURBOS_RES':\r\n\t\t\t\t\t\tconsole.log('收到消息:', dataObj);\r\n\t\t\t\t\t\ttargetAI = this.enabledAIs.find(ai => ai.name === 'TurboS@元器');\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'RETURN_TURBOS_LARGE_RES':\r\n\t\t\t\t\t\tconsole.log('收到消息:', dataObj);\r\n\t\t\t\t\t\ttargetAI = this.enabledAIs.find(ai => ai.name === 'TurboS长文版@元器');\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (targetAI) {\r\n\t\t\t\t\t// 更新AI状态为已完成\r\n\t\t\t\t\ttargetAI.status = 'completed';\r\n\r\n\t\t\t\t\t// 将最后一条进度消息标记为已完成\r\n\t\t\t\t\tif (targetAI.progressLogs.length > 0) {\r\n\t\t\t\t\t\ttargetAI.progressLogs[0].isCompleted = true;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 添加结果到数组开头\r\n\t\t\t\t\tconst resultIndex = this.results.findIndex(r => r.aiName === targetAI.name);\r\n\t\t\t\t\tif (resultIndex === -1) {\r\n\t\t\t\t\t\tthis.results.unshift({\r\n\t\t\t\t\t\t\taiName: targetAI.name,\r\n\t\t\t\t\t\t\tcontent: dataObj.draftContent,\r\n\t\t\t\t\t\t\tshareUrl: dataObj.shareUrl || '',\r\n\t\t\t\t\t\t\tshareImgUrl: dataObj.shareImgUrl || '',\r\n\t\t\t\t\t\t\ttimestamp: new Date()\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.activeResultIndex = 0;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.results.splice(resultIndex, 1);\r\n\t\t\t\t\t\tthis.results.unshift({\r\n\t\t\t\t\t\t\taiName: targetAI.name,\r\n\t\t\t\t\t\t\tcontent: dataObj.draftContent,\r\n\t\t\t\t\t\t\tshareUrl: dataObj.shareUrl || '',\r\n\t\t\t\t\t\t\tshareImgUrl: dataObj.shareImgUrl || '',\r\n\t\t\t\t\t\t\ttimestamp: new Date()\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.activeResultIndex = 0;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 折叠所有区域当有结果返回时\r\n\t\t\t\t\tthis.sectionExpanded.aiConfig = false;\r\n\t\t\t\t\tthis.sectionExpanded.promptInput = false;\r\n\t\t\t\t\tthis.sectionExpanded.taskStatus = false;\r\n\r\n\t\t\t\t\t// 滚动到结果区域\r\n\t\t\t\t\tthis.scrollIntoView = 'results';\r\n\r\n\t\t\t\t\t// 保存历史记录\r\n\t\t\t\t\tthis.saveHistory();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 状态相关方法\r\n\t\t\tgetStatusText(status) {\r\n\t\t\t\tconst statusMap = {\r\n\t\t\t\t\t'idle': '等待中',\r\n\t\t\t\t\t'running': '正在执行',\r\n\t\t\t\t\t'completed': '已完成',\r\n\t\t\t\t\t'failed': '执行失败'\r\n\t\t\t\t};\r\n\t\t\t\treturn statusMap[status] || '未知状态';\r\n\t\t\t},\r\n\r\n\t\t\tgetStatusIconClass(status) {\r\n\t\t\t\tconst classMap = {\r\n\t\t\t\t\t'idle': 'status-idle',\r\n\t\t\t\t\t'running': 'status-running',\r\n\t\t\t\t\t'completed': 'status-completed',\r\n\t\t\t\t\t'failed': 'status-failed'\r\n\t\t\t\t};\r\n\t\t\t\treturn classMap[status] || 'status-unknown';\r\n\t\t\t},\r\n\r\n\t\t\tgetStatusEmoji(status) {\r\n\t\t\t\tconst emojiMap = {\r\n\t\t\t\t\t'idle': '⏳',\r\n\t\t\t\t\t'running': '🔄',\r\n\t\t\t\t\t'completed': '✅',\r\n\t\t\t\t\t'failed': '❌'\r\n\t\t\t\t};\r\n\t\t\t\treturn emojiMap[status] || '❓';\r\n\t\t\t},\r\n\r\n\t\t\t// 切换任务展开状态\r\n\t\t\ttoggleTaskExpansion(ai) {\r\n\t\t\t\tai.isExpanded = !ai.isExpanded;\r\n\t\t\t},\r\n\r\n\t\t\t// 切换自动播放\r\n\t\t\ttoggleAutoPlay(event) {\r\n\t\t\t\tthis.autoPlay = event.detail.value;\r\n\t\t\t},\r\n\r\n\t\t\t// 预览图片\r\n\t\t\tpreviewImage(url) {\r\n\t\t\t\tuni.previewImage({\r\n\t\t\t\t\tcurrent: url,\r\n\t\t\t\t\turls: [url]\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 结果相关方法\r\n\t\t\tswitchResultTab(index) {\r\n\t\t\t\tthis.activeResultIndex = index;\r\n\t\t\t},\r\n\r\n\t\t\trenderMarkdown(text) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\treturn marked(text);\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\treturn text;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tisImageFile(url) {\r\n\t\t\t\tif (!url) return false;\r\n\t\t\t\tconst imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];\r\n\t\t\t\tconst urlLower = url.toLowerCase();\r\n\t\t\t\treturn imageExtensions.some(ext => urlLower.includes(ext));\r\n\t\t\t},\r\n\r\n\t\t\t// 判断是否为PDF文件\r\n\t\t\tisPdfFile(url) {\r\n\t\t\t\tif (!url) return false;\r\n\t\t\t\treturn url.toLowerCase().includes('.pdf');\r\n\t\t\t},\r\n\r\n\t\t\tcopyResult(content) {\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\tdata: content,\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '已复制到剪贴板',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 收录公众号\r\n\t\t\tasync collectToOffice(content) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '正在收录...'\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// 自增计数器\r\n\t\t\t\t\tthis.collectNum++;\r\n\r\n\t\t\t\t\tconst params = {\r\n\t\t\t\t\t\tcontentText: content,\r\n\t\t\t\t\t\tuserId: this.userId,\r\n\t\t\t\t\t\tshareUrl: this.currentResult.shareUrl || '',\r\n\t\t\t\t\t\taiName: this.currentResult.aiName || '',\r\n\t\t\t\t\t\tnum: this.collectNum\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\tconst res = await pushAutoOffice(params);\r\n\r\n\t\t\t\t\tuni.hideLoading();\r\n\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: `收录成功(${this.collectNum})`,\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.message || '收录失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tconsole.error('收录公众号失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '收录失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// shareResult(result) {\r\n\t\t\t// \tuni.share({\r\n\t\t\t// \t\tprovider: 'weixin',\r\n\t\t\t// \t\tscene: 'WXSceneSession',\r\n\t\t\t// \t\ttype: 0,\r\n\t\t\t// \t\ttitle: `${result.aiName}的执行结果`,\r\n\t\t\t// \t\tsummary: result.content.substring(0, 100),\r\n\t\t\t// \t\tsuccess: () => {\r\n\t\t\t// \t\t\tuni.showToast({\r\n\t\t\t// \t\t\t\ttitle: '分享成功',\r\n\t\t\t// \t\t\t\ticon: 'success'\r\n\t\t\t// \t\t\t});\r\n\t\t\t// \t\t}\r\n\t\t\t// \t});\r\n\t\t\t// },\r\n\r\n\t\t\texportResult(result) {\r\n\t\t\t\t// 小程序环境下的导出功能可以通过分享或复制实现\r\n\t\t\t\tthis.copyResult(result.content);\r\n\t\t\t},\r\n\r\n\t\t\topenShareUrl(url) {\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\tdata: url,\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '原链接已复制',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '复制失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 复制PDF链接\r\n\t\t\tcopyPdfUrl(url) {\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\tdata: url,\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: 'PDF链接已复制',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '复制失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 打开PDF文件\r\n\t\t\topenPdfFile(url) {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在下载PDF...'\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 尝试下载并打开文件\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\turl: url,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\t\t\t// 打开文件\r\n\t\t\t\t\t\t\tuni.openDocument({\r\n\t\t\t\t\t\t\t\tfilePath: res.tempFilePath,\r\n\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: 'PDF已打开',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\t\t\t\t// 如果无法打开，提示并复制链接\r\n\t\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t\t\t\t\tcontent: '无法在当前环境打开PDF文件，已复制链接到剪贴板，请在浏览器中打开',\r\n\t\t\t\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\t\t\t\t\t\t\t\tdata: url\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '下载失败',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t// 下载失败，提示并复制链接\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t\tcontent: '下载失败，已复制PDF链接到剪贴板，请在浏览器中打开',\r\n\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\t\t\t\t\tdata: url\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 历史记录相关方法\r\n\t\t\tshowHistoryDrawer() {\r\n\t\t\t\tthis.historyDrawerVisible = true;\r\n\t\t\t\tthis.loadChatHistory(1);\r\n\t\t\t},\r\n\r\n\t\t\tcloseHistoryDrawer() {\r\n\t\t\t\tthis.historyDrawerVisible = false;\r\n\t\t\t},\r\n\r\n\t\t\tasync loadChatHistory(isAll) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await getChatHistory(this.userId, isAll);\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\tthis.chatHistory = res.data || [];\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('加载历史记录失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '加载历史记录失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tloadHistoryItem(item) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst historyData = JSON.parse(item.data);\r\n\t\t\t\t\t// 恢复AI选择配置\r\n\t\t\t\t\tthis.aiList = historyData.aiList || this.aiList;\r\n\t\t\t\t\t// 恢复提示词输入\r\n\t\t\t\t\tthis.promptInput = historyData.promptInput || item.userPrompt;\r\n\t\t\t\t\t// 恢复任务流程\r\n\t\t\t\t\tthis.enabledAIs = historyData.enabledAIs || [];\r\n\t\t\t\t\t// 恢复主机可视化，确保URL格式正确\r\n\t\t\t\t\tconst screenshots = historyData.screenshots || [];\r\n\t\t\t\t\tthis.screenshots = screenshots.map(url => {\r\n\t\t\t\t\t\treturn url.startsWith('http') ? url : `http://localhost${url}`;\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// 恢复执行结果\r\n\t\t\t\t\tthis.results = historyData.results || [];\r\n\t\t\t\t\t// 恢复chatId\r\n\t\t\t\t\tthis.chatId = item.chatId || this.chatId;\r\n\t\t\t\t\tthis.userInfoReq.toneChatId = item.toneChatId || '';\r\n\t\t\t\t\tthis.userInfoReq.ybDsChatId = item.ybDsChatId || '';\r\n\t\t\t\t\tthis.userInfoReq.dbChatId = item.dbChatId || '';\r\n\t\t\t\t\tthis.userInfoReq.isNewChat = false;\r\n\r\n\t\t\t\t\t// 不再根据AI登录状态更新AI启用状态，保持原有选择\r\n\r\n\t\t\t\t\t// 展开相关区域\r\n\t\t\t\t\tthis.sectionExpanded.aiConfig = true;\r\n\t\t\t\t\tthis.sectionExpanded.promptInput = true;\r\n\t\t\t\t\tthis.sectionExpanded.taskStatus = true;\r\n\t\t\t\t\tthis.taskStarted = true;\r\n\r\n\t\t\t\t\tthis.closeHistoryDrawer();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '历史记录加载成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('加载历史记录失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '加载失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 加载上次会话\r\n\t\t\tasync loadLastChat() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await getChatHistory(this.userId, 0);\r\n\t\t\t\t\tif (res.code === 200 && res.data && res.data.length > 0) {\r\n\t\t\t\t\t\t// 获取最新的会话记录\r\n\t\t\t\t\t\tconst lastChat = res.data[0];\r\n\t\t\t\t\t\tthis.loadHistoryItem(lastChat);\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('加载上次会话失败:', error);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tasync saveHistory() {\r\n\t\t\t\tconst historyData = {\r\n\t\t\t\t\taiList: this.aiList,\r\n\t\t\t\t\tpromptInput: this.promptInput,\r\n\t\t\t\t\tenabledAIs: this.enabledAIs,\r\n\t\t\t\t\tscreenshots: this.screenshots,\r\n\t\t\t\t\tresults: this.results,\r\n\t\t\t\t\tchatId: this.chatId,\r\n\t\t\t\t\ttoneChatId: this.userInfoReq.toneChatId,\r\n\t\t\t\t\tybDsChatId: this.userInfoReq.ybDsChatId,\r\n\t\t\t\t\tdbChatId: this.userInfoReq.dbChatId\r\n\t\t\t\t};\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tawait saveUserChatData({\r\n\t\t\t\t\t\tuserId: this.userId,\r\n\t\t\t\t\t\tuserPrompt: this.promptInput,\r\n\t\t\t\t\t\tdata: JSON.stringify(historyData),\r\n\t\t\t\t\t\tchatId: this.chatId,\r\n\t\t\t\t\t\ttoneChatId: this.userInfoReq.toneChatId,\r\n\t\t\t\t\t\tybDsChatId: this.userInfoReq.ybDsChatId,\r\n\t\t\t\t\t\tdbChatId: this.userInfoReq.dbChatId\r\n\t\t\t\t\t});\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('保存历史记录失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '保存历史记录失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tgetHistoryDate(timestamp) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconsole.log('getHistoryDate 输入:', timestamp, typeof timestamp);\r\n\r\n\t\t\t\t\tif (!timestamp) {\r\n\t\t\t\t\t\treturn '未知日期';\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tlet date;\r\n\r\n\t\t\t\t\tif (typeof timestamp === 'number') {\r\n\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t} else if (typeof timestamp === 'string') {\r\n\t\t\t\t\t\t// 处理 \"2025-6-23 14:53:12\" 这种格式\r\n\t\t\t\t\t\tconst match = timestamp.match(/(\\d{4})-(\\d{1,2})-(\\d{1,2})\\s+(\\d{1,2}):(\\d{1,2}):(\\d{1,2})/);\r\n\t\t\t\t\t\tif (match) {\r\n\t\t\t\t\t\t\tconst [, year, month, day, hour, minute, second] = match;\r\n\t\t\t\t\t\t\tdate = new Date(\r\n\t\t\t\t\t\t\t\tparseInt(year),\r\n\t\t\t\t\t\t\t\tparseInt(month) - 1,\r\n\t\t\t\t\t\t\t\tparseInt(day),\r\n\t\t\t\t\t\t\t\tparseInt(hour),\r\n\t\t\t\t\t\t\t\tparseInt(minute),\r\n\t\t\t\t\t\t\t\tparseInt(second)\r\n\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 如果正则不匹配，尝试其他方式\r\n\t\t\t\t\t\t\tconst fixedTimestamp = timestamp.replace(/\\s/g, 'T');\r\n\t\t\t\t\t\t\tdate = new Date(fixedTimestamp);\r\n\r\n\t\t\t\t\t\t\tif (isNaN(date.getTime())) {\r\n\t\t\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tconsole.log('getHistoryDate 解析结果:', date, date.getTime());\r\n\r\n\t\t\t\t\tif (isNaN(date.getTime())) {\r\n\t\t\t\t\t\treturn '未知日期';\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tconst today = new Date();\r\n\t\t\t\t\tconst yesterday = new Date(today);\r\n\t\t\t\t\tyesterday.setDate(yesterday.getDate() - 1);\r\n\r\n\t\t\t\t\tif (date.toDateString() === today.toDateString()) {\r\n\t\t\t\t\t\treturn '今天';\r\n\t\t\t\t\t} else if (date.toDateString() === yesterday.toDateString()) {\r\n\t\t\t\t\t\treturn '昨天';\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn date.toLocaleDateString('zh-CN');\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('格式化日期错误:', error, timestamp);\r\n\t\t\t\t\treturn '未知日期';\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 格式化历史记录时间\r\n\t\t\tformatHistoryTime(timestamp) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconsole.log('formatHistoryTime 输入:', timestamp, typeof timestamp);\r\n\r\n\t\t\t\t\tlet date;\r\n\r\n\t\t\t\t\tif (!timestamp) {\r\n\t\t\t\t\t\treturn '时间未知';\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 如果是数字，直接创建Date对象\r\n\t\t\t\t\tif (typeof timestamp === 'number') {\r\n\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t} else if (typeof timestamp === 'string') {\r\n\t\t\t\t\t\t// 处理ISO 8601格式：2025-06-25T07:18:54.110Z\r\n\t\t\t\t\t\tif (timestamp.includes('T') && (timestamp.includes('Z') || timestamp.includes('+'))) {\r\n\t\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// 处理 \"2025-6-26 08:46:26\" 这种格式\r\n\t\t\t\t\t\telse {\r\n\t\t\t\t\t\t\tconst match = timestamp.match(/(\\d{4})-(\\d{1,2})-(\\d{1,2})\\s+(\\d{1,2}):(\\d{1,2}):(\\d{1,2})/);\r\n\t\t\t\t\t\t\tif (match) {\r\n\t\t\t\t\t\t\t\tconst [, year, month, day, hour, minute, second] = match;\r\n\t\t\t\t\t\t\t\t// 注意：Date构造函数的month参数是0-11，所以要减1\r\n\t\t\t\t\t\t\t\tdate = new Date(\r\n\t\t\t\t\t\t\t\t\tparseInt(year),\r\n\t\t\t\t\t\t\t\t\tparseInt(month) - 1,\r\n\t\t\t\t\t\t\t\t\tparseInt(day),\r\n\t\t\t\t\t\t\t\t\tparseInt(hour),\r\n\t\t\t\t\t\t\t\t\tparseInt(minute),\r\n\t\t\t\t\t\t\t\t\tparseInt(second)\r\n\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t// 如果正则不匹配，尝试其他方式\r\n\t\t\t\t\t\t\t\tconst fixedTimestamp = timestamp.replace(/\\s/g, 'T');\r\n\t\t\t\t\t\t\t\tdate = new Date(fixedTimestamp);\r\n\r\n\t\t\t\t\t\t\t\tif (isNaN(date.getTime())) {\r\n\t\t\t\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else if (timestamp instanceof Date) {\r\n\t\t\t\t\t\tdate = timestamp;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tconsole.log('formatHistoryTime 解析结果:', date, date.getTime());\r\n\r\n\t\t\t\t\tif (isNaN(date.getTime())) {\r\n\t\t\t\t\t\treturn '时间未知';\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 使用更简洁的时间格式，避免显示时区信息\r\n\t\t\t\t\tconst hour = date.getHours().toString().padStart(2, '0');\r\n\t\t\t\t\tconst minute = date.getMinutes().toString().padStart(2, '0');\r\n\r\n\t\t\t\t\tconst timeString = `${hour}:${minute}`;\r\n\r\n\t\t\t\t\tconsole.log('formatHistoryTime 输出:', timeString);\r\n\t\t\t\t\treturn timeString;\r\n\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('格式化时间错误:', error, timestamp);\r\n\t\t\t\t\treturn '时间未知';\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 修改折叠切换方法\r\n\t\t\ttoggleHistoryExpansion(item) {\r\n\t\t\t\tthis.expandedHistoryItems[item.chatId] = !this.expandedHistoryItems[item.chatId];\r\n\t\t\t\tthis.$forceUpdate(); // 强制更新视图\r\n\t\t\t},\r\n\r\n\t\t\t// 智能评分相关方法\r\n\t\t\tshowScoreModal() {\r\n\t\t\t\tthis.selectedResults = [];\r\n\t\t\t\tthis.scoreModalVisible = true;\r\n\t\t\t},\r\n\r\n\t\t\tcloseScoreModal() {\r\n\t\t\t\tthis.scoreModalVisible = false;\r\n\t\t\t},\r\n\r\n\t\t\ttoggleResultSelection(event) {\r\n\t\t\t\tconst values = event.detail.value;\r\n\t\t\t\tconsole.log('toggleResultSelection - 选中的values:', values);\r\n\t\t\t\tconsole.log('toggleResultSelection - 当前scorePrompt:', this.scorePrompt.trim());\r\n\t\t\t\tthis.selectedResults = values;\r\n\t\t\t\tconsole.log('toggleResultSelection - 更新后的selectedResults:', this.selectedResults);\r\n\t\t\t\tconsole.log('toggleResultSelection - canScore状态:', this.canScore);\r\n\t\t\t},\r\n\r\n\t\t\thandleScore() {\r\n\t\t\t\tif (!this.canScore) return;\r\n\r\n\t\t\t\t// 获取选中的结果内容并按照指定格式拼接\r\n\t\t\t\tconst selectedContents = this.results\r\n\t\t\t\t\t.filter(result => this.selectedResults.includes(result.aiName))\r\n\t\t\t\t\t.map(result => {\r\n\t\t\t\t\t\t// 将HTML内容转换为纯文本（小程序版本简化处理）\r\n\t\t\t\t\t\tconst plainContent = result.content.replace(/<[^>]*>/g, '');\r\n\t\t\t\t\t\treturn `${result.aiName}初稿：\\n${plainContent}\\n`;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.join('\\n');\r\n\r\n\t\t\t\t// 构建完整的评分提示内容\r\n\t\t\t\tconst fullPrompt = `${this.scorePrompt}\\n${selectedContents}`;\r\n\r\n\t\t\t\t// 构建评分请求\r\n\t\t\t\tconst scoreRequest = {\r\n\t\t\t\t\tjsonrpc: '2.0',\r\n\t\t\t\t\tid: this.generateUUID(),\r\n\t\t\t\t\tmethod: 'AI评分',\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\ttaskId: this.generateUUID(),\r\n\t\t\t\t\t\tuserId: this.userId,\r\n\t\t\t\t\t\tcorpId: this.corpId,\r\n\t\t\t\t\t\tuserPrompt: fullPrompt,\r\n\t\t\t\t\t\troles: 'zj-db-sdsk' // 默认使用豆包进行评分\r\n\t\t\t\t\t}\r\n\t\t\t\t};\r\n\r\n\t\t\t\t// 发送评分请求\r\n\t\t\t\tconsole.log(\"参数\", scoreRequest);\r\n\t\t\t\tthis.message(scoreRequest);\r\n\t\t\t\tthis.closeScoreModal();\r\n\r\n\t\t\t\t// 创建智能评分AI节点\r\n\t\t\t\tconst wkpfAI = {\r\n\t\t\t\t\tname: '智能评分',\r\n\t\t\t\t\tavatar: 'https://u3w.com/chatfile/%E8%B1%86%E5%8C%85.png',\r\n\t\t\t\t\tcapabilities: [],\r\n\t\t\t\t\tselectedCapabilities: [],\r\n\t\t\t\t\tenabled: true,\r\n\t\t\t\t\tstatus: 'running',\r\n\t\t\t\t\tprogressLogs: [\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tcontent: '智能评分任务已提交，正在评分...',\r\n\t\t\t\t\t\t\ttimestamp: new Date(),\r\n\t\t\t\t\t\t\tisCompleted: false,\r\n\t\t\t\t\t\t\ttype: '智能评分'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t],\r\n\t\t\t\t\tisExpanded: true\r\n\t\t\t\t};\r\n\r\n\t\t\t\t// 检查是否已存在智能评分\r\n\t\t\t\tconst existIndex = this.enabledAIs.findIndex(ai => ai.name === '智能评分');\r\n\t\t\t\tif (existIndex === -1) {\r\n\t\t\t\t\t// 如果不存在，添加到数组开头\r\n\t\t\t\t\tthis.enabledAIs.unshift(wkpfAI);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果已存在，更新状态和日志\r\n\t\t\t\t\tthis.enabledAIs[existIndex] = wkpfAI;\r\n\t\t\t\t\t// 将智能评分移到数组开头\r\n\t\t\t\t\tconst wkpf = this.enabledAIs.splice(existIndex, 1)[0];\r\n\t\t\t\t\tthis.enabledAIs.unshift(wkpf);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '评分请求已发送，请等待结果',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 创建新对话\r\n\t\t\tcreateNewChat() {\r\n\t\t\t\t// 重置所有数据\r\n\t\t\t\tthis.chatId = this.generateUUID();\r\n\t\t\t\tthis.promptInput = '';\r\n\t\t\t\tthis.taskStarted = false;\r\n\t\t\t\tthis.screenshots = [];\r\n\t\t\t\tthis.results = [];\r\n\t\t\t\tthis.enabledAIs = [];\r\n\t\t\t\tthis.userInfoReq = {\r\n\t\t\t\t\tuserPrompt: '',\r\n\t\t\t\t\tuserId: this.userId,\r\n\t\t\t\t\tcorpId: this.corpId,\r\n\t\t\t\t\ttaskId: '',\r\n\t\t\t\t\troles: '',\r\n\t\t\t\t\ttoneChatId: '',\r\n\t\t\t\t\tybDsChatId: '',\r\n\t\t\t\t\tdbChatId: '',\r\n\t\t\t\t\tisNewChat: true\r\n\t\t\t\t};\r\n\t\t\t\t// 重置AI列表为初始状态\r\n\t\t\t\tthis.aiList = [{\r\n\t\t\t\t\t\tname: 'TurboS@元器',\r\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/yuanbao.png',\r\n\t\t\t\t\t\tcapabilities: [],\r\n\t\t\t\t\t\tselectedCapabilities: [],\r\n\t\t\t\t\t\tenabled: true,\r\n\t\t\t\t\t\tstatus: 'idle',\r\n\t\t\t\t\t\tprogressLogs: [],\r\n\t\t\t\t\t\tisExpanded: true\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: 'TurboS长文版@元器',\r\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/yuanbao.png',\r\n\t\t\t\t\t\tcapabilities: [],\r\n\t\t\t\t\t\tselectedCapabilities: [],\r\n\t\t\t\t\t\tenabled: true,\r\n\t\t\t\t\t\tstatus: 'idle',\r\n\t\t\t\t\t\tprogressLogs: [],\r\n\t\t\t\t\t\tisExpanded: true\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '腾讯元宝T1',\r\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/yuanbao.png',\r\n\t\t\t\t\t\tcapabilities: [{\r\n\t\t\t\t\t\t\t\tlabel: '深度思考',\r\n\t\t\t\t\t\t\t\tvalue: 'deep_thinking'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tlabel: '联网搜索',\r\n\t\t\t\t\t\t\t\tvalue: 'web_search'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t],\r\n\t\t\t\t\t\tselectedCapabilities: ['deep_thinking', 'web_search'],\r\n\t\t\t\t\t\tenabled: true,\r\n\t\t\t\t\t\tstatus: 'idle',\r\n\t\t\t\t\t\tprogressLogs: [],\r\n\t\t\t\t\t\tisExpanded: true\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '腾讯元宝DS',\r\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/yuanbao.png',\r\n\t\t\t\t\t\tcapabilities: [{\r\n\t\t\t\t\t\t\t\tlabel: '深度思考',\r\n\t\t\t\t\t\t\t\tvalue: 'deep_thinking'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tlabel: '联网搜索',\r\n\t\t\t\t\t\t\t\tvalue: 'web_search'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t],\r\n\t\t\t\t\t\tselectedCapabilities: ['deep_thinking', 'web_search'],\r\n\t\t\t\t\t\tenabled: true,\r\n\t\t\t\t\t\tstatus: 'idle',\r\n\t\t\t\t\t\tprogressLogs: [],\r\n\t\t\t\t\t\tisExpanded: true\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '豆包',\r\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/%E8%B1%86%E5%8C%85.png',\r\n\t\t\t\t\t\tcapabilities: [{\r\n\t\t\t\t\t\t\tlabel: '深度思考',\r\n\t\t\t\t\t\t\tvalue: 'deep_thinking'\r\n\t\t\t\t\t\t}],\r\n\t\t\t\t\t\tselectedCapabilities: ['deep_thinking'],\r\n\t\t\t\t\t\tenabled: true,\r\n\t\t\t\t\t\tstatus: 'idle',\r\n\t\t\t\t\t\tprogressLogs: [],\r\n\t\t\t\t\t\tisExpanded: true\r\n\t\t\t\t\t}\r\n\t\t\t\t];\r\n\t\t\t\t// 不再根据AI登录状态更新AI启用状态，保持原有选择\r\n\r\n\t\t\t\t// 展开相关区域\r\n\t\t\t\tthis.sectionExpanded.aiConfig = true;\r\n\t\t\t\tthis.sectionExpanded.promptInput = true;\r\n\t\t\t\tthis.sectionExpanded.taskStatus = true;\r\n\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '已创建新对话',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// AI状态相关方法\r\n\t\t\tcheckAiLoginStatus() {\r\n\t\t\t\t// 延迟检查，确保WebSocket连接已建立\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.sendAiStatusCheck();\r\n\t\t\t\t\t// 不再更新AI启用状态，保持原有选择\r\n\t\t\t\t}, 2000);\r\n\t\t\t},\r\n\r\n\t\t\tsendAiStatusCheck() {\r\n\t\t\t\t// 检查腾讯元宝登录状态\r\n\t\t\t\tthis.sendWebSocketMessage({\r\n\t\t\t\t\ttype: 'PLAY_CHECK_YB_LOGIN',\r\n\t\t\t\t\tuserId: this.userId,\r\n\t\t\t\t\tcorpId: this.corpId\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 检查豆包登录状态\r\n\t\t\t\tthis.sendWebSocketMessage({\r\n\t\t\t\t\ttype: 'PLAY_CHECK_DB_LOGIN',\r\n\t\t\t\t\tuserId: this.userId,\r\n\t\t\t\t\tcorpId: this.corpId\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 检查智能体登录状态\r\n\t\t\t\tthis.sendWebSocketMessage({\r\n\t\t\t\t\ttype: 'PLAY_CHECK_AGENT_LOGIN',\r\n\t\t\t\t\tuserId: this.userId,\r\n\t\t\t\t\tcorpId: this.corpId\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tgetPlatformIcon(type) {\r\n\t\t\t\tconst icons = {\r\n\t\t\t\t\tyuanbao: 'https://u3w.com/chatfile/yuanbao.png',\r\n\t\t\t\t\tdoubao: 'https://u3w.com/chatfile/%E8%B1%86%E5%8C%85.png',\r\n\t\t\t\t\tagent: 'https://u3w.com/chatfile/yuanbao.png'\r\n\t\t\t\t};\r\n\t\t\t\treturn icons[type] || '';\r\n\t\t\t},\r\n\r\n\t\t\tgetPlatformName(type) {\r\n\t\t\t\tconst names = {\r\n\t\t\t\t\tyuanbao: '腾讯元宝',\r\n\t\t\t\t\tdoubao: '豆包',\r\n\t\t\t\t\tagent: '智能体'\r\n\t\t\t\t};\r\n\t\t\t\treturn names[type] || '';\r\n\t\t\t},\r\n\r\n\r\n\r\n\r\n\r\n\t\t\trefreshAiStatus() {\r\n\t\t\t\t// 重置所有AI状态为加载中\r\n\t\t\t\tthis.isLoading = {\r\n\t\t\t\t\tyuanbao: true,\r\n\t\t\t\t\tdoubao: true,\r\n\t\t\t\t\tagent: true\r\n\t\t\t\t};\r\n\r\n\t\t\t\t// 重置登录状态\r\n\t\t\t\tthis.aiLoginStatus = {\r\n\t\t\t\t\tyuanbao: false,\r\n\t\t\t\t\tdoubao: false,\r\n\t\t\t\t\tagent: false\r\n\t\t\t\t};\r\n\r\n\t\t\t\t// 重置账户信息\r\n\t\t\t\tthis.accounts = {\r\n\t\t\t\t\tyuanbao: '',\r\n\t\t\t\t\tdoubao: '',\r\n\t\t\t\t\tagent: ''\r\n\t\t\t\t};\r\n\r\n\t\t\t\t// 显示刷新提示\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '正在刷新连接状态...',\r\n\t\t\t\t\ticon: 'loading',\r\n\t\t\t\t\tduration: 1500\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 重新建立WebSocket连接\r\n\t\t\t\tthis.closeWebSocket();\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.initWebSocket();\r\n\t\t\t\t\t// 延迟检查AI状态，确保WebSocket重新连接\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.sendAiStatusCheck();\r\n\t\t\t\t\t}, 2000);\r\n\t\t\t\t}, 500);\r\n\t\t\t},\r\n\r\n\t\t\t// 判断AI是否已登录可用\r\n\t\t\tisAiLoginEnabled(ai) {\r\n\t\t\t\tswitch (ai.name) {\r\n\t\t\t\t\tcase 'TurboS@元器':\r\n\t\t\t\t\tcase 'TurboS长文版@元器':\r\n\t\t\t\t\t\treturn this.aiLoginStatus.agent; // 智能体登录状态\r\n\t\t\t\t\tcase '腾讯元宝T1':\r\n\t\t\t\t\tcase '腾讯元宝DS':\r\n\t\t\t\t\t\treturn this.aiLoginStatus.yuanbao; // 腾讯元宝登录状态\r\n\t\t\t\t\tcase '豆包':\r\n\t\t\t\t\t\treturn this.aiLoginStatus.doubao; // 豆包登录状态\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 判断AI是否在加载状态\r\n\t\t\tisAiInLoading(ai) {\r\n\t\t\t\tswitch (ai.name) {\r\n\t\t\t\t\tcase 'TurboS@元器':\r\n\t\t\t\t\tcase 'TurboS长文版@元器':\r\n\t\t\t\t\t\treturn this.isLoading.agent;\r\n\t\t\t\t\tcase '腾讯元宝T1':\r\n\t\t\t\t\tcase '腾讯元宝DS':\r\n\t\t\t\t\t\treturn this.isLoading.yuanbao;\r\n\t\t\t\t\tcase '豆包':\r\n\t\t\t\t\t\treturn this.isLoading.doubao;\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 根据登录状态禁用相关AI（已废弃，不再修改enabled状态）\r\n\t\t\tdisableAIsByLoginStatus(loginType) {\r\n\t\t\t\t// 不再修改enabled状态，只通过UI控制操作权限\r\n\t\t\t\tconsole.log(`AI ${loginType} 登录状态已更新，但保持原有选择`);\r\n\t\t\t},\r\n\r\n\t\t\t// 根据当前AI登录状态更新AI启用状态（已废弃，不再修改enabled状态）\r\n\t\t\tupdateAiEnabledStatus() {\r\n\t\t\t\t// 不再修改enabled状态，只通过UI控制操作权限\r\n\t\t\t\tconsole.log('AI登录状态已更新，但保持原有选择');\r\n\t\t\t},\r\n\r\n\t\t\t// 格式化时间\r\n\t\t\tformatTime(timestamp) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconsole.log('formatTime 输入:', timestamp, typeof timestamp);\r\n\r\n\t\t\t\t\tif (!timestamp) {\r\n\t\t\t\t\t\treturn '时间未知';\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tlet date;\r\n\r\n\t\t\t\t\tif (typeof timestamp === 'number') {\r\n\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t} else if (typeof timestamp === 'string') {\r\n\t\t\t\t\t\t// 处理ISO 8601格式：2025-06-25T07:18:54.110Z\r\n\t\t\t\t\t\tif (timestamp.includes('T') && (timestamp.includes('Z') || timestamp.includes('+'))) {\r\n\t\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// 处理 \"2025-6-23 14:53:12\" 这种格式\r\n\t\t\t\t\t\telse {\r\n\t\t\t\t\t\t\tconst match = timestamp.match(/(\\d{4})-(\\d{1,2})-(\\d{1,2})\\s+(\\d{1,2}):(\\d{1,2}):(\\d{1,2})/);\r\n\t\t\t\t\t\t\tif (match) {\r\n\t\t\t\t\t\t\t\tconst [, year, month, day, hour, minute, second] = match;\r\n\t\t\t\t\t\t\t\tdate = new Date(\r\n\t\t\t\t\t\t\t\t\tparseInt(year),\r\n\t\t\t\t\t\t\t\t\tparseInt(month) - 1,\r\n\t\t\t\t\t\t\t\t\tparseInt(day),\r\n\t\t\t\t\t\t\t\t\tparseInt(hour),\r\n\t\t\t\t\t\t\t\t\tparseInt(minute),\r\n\t\t\t\t\t\t\t\t\tparseInt(second)\r\n\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t// 如果正则不匹配，尝试其他方式\r\n\t\t\t\t\t\t\t\tconst fixedTimestamp = timestamp.replace(/\\s/g, 'T');\r\n\t\t\t\t\t\t\t\tdate = new Date(fixedTimestamp);\r\n\r\n\t\t\t\t\t\t\t\tif (isNaN(date.getTime())) {\r\n\t\t\t\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else if (timestamp instanceof Date) {\r\n\t\t\t\t\t\tdate = timestamp;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tconsole.log('formatTime 解析结果:', date, date.getTime());\r\n\r\n\t\t\t\t\tif (isNaN(date.getTime())) {\r\n\t\t\t\t\t\treturn '时间未知';\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 使用更简洁的时间格式，避免显示时区信息\r\n\t\t\t\t\tconst hour = date.getHours().toString().padStart(2, '0');\r\n\t\t\t\t\tconst minute = date.getMinutes().toString().padStart(2, '0');\r\n\t\t\t\t\tconst second = date.getSeconds().toString().padStart(2, '0');\r\n\r\n\t\t\t\t\tconst timeString = `${hour}:${minute}:${second}`;\r\n\r\n\t\t\t\t\tconsole.log('formatTime 输出:', timeString);\r\n\t\t\t\t\treturn timeString;\r\n\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('格式化时间错误:', error, timestamp);\r\n\t\t\t\t\treturn '时间未知';\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style scoped>\r\n\t.console-container {\r\n\t\theight: 100vh;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t/* 顶部固定区域 */\r\n\t.header-fixed {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tz-index: 1000;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-bottom: 1px solid #ebeef5;\r\n\t}\r\n\r\n\t.header-content {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 10px 15px;\r\n\t\tpadding-top: calc(10px + var(--status-bar-height));\r\n\t}\r\n\r\n\t.header-title {\r\n\t\tfont-size: 18px;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #303133;\r\n\t}\r\n\r\n\t.header-actions {\r\n\t\tdisplay: flex;\r\n\t\tgap: 10px;\r\n\t}\r\n\r\n\t.action-btn {\r\n\t\twidth: 36px;\r\n\t\theight: 36px;\r\n\t\tborder-radius: 18px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.action-btn:active {\r\n\t\ttransform: scale(0.92);\r\n\t\topacity: 0.7;\r\n\t}\r\n\r\n\t.action-icon {\r\n\t\tfont-size: 18px;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: 500;\r\n\t\ttext-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 1;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.action-icon-img {\r\n\t\twidth: 20px;\r\n\t\theight: 20px;\r\n\t\tz-index: 1;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t/* 创建新会话图标更大 */\r\n\t.new-chat-btn .action-icon-img {\r\n\t\twidth: 24px;\r\n\t\theight: 24px;\r\n\t}\r\n\r\n\t/* 移除渐变背景，使用原生图标 */\r\n\t.refresh-btn,\r\n\t.history-btn,\r\n\t.new-chat-btn {\r\n\t\tbackground: transparent;\r\n\t\tbox-shadow: none;\r\n\t}\r\n\r\n\r\n\r\n\t/* 主体滚动区域 */\r\n\t.main-scroll {\r\n\t\tflex: 1;\r\n\t\theight: calc(100vh - 52px - var(--status-bar-height));\r\n\t\tpadding-top: calc(52px + var(--status-bar-height));\r\n\t\tpadding-bottom: 20px;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t/* 区块样式 */\r\n\t.section-block {\r\n\t\tmargin: 10px 15px;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 8px;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.section-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 15px;\r\n\t\tborder-bottom: 1px solid #ebeef5;\r\n\t\tbackground-color: #fafafa;\r\n\t}\r\n\r\n\t.section-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #303133;\r\n\t}\r\n\r\n\t.section-arrow {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #909399;\r\n\t\ttransition: transform 0.3s;\r\n\t}\r\n\r\n\t.task-arrow {\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #909399;\r\n\t\ttransition: transform 0.3s;\r\n\t\tmargin-right: 8px;\r\n\t}\r\n\r\n\t.close-icon {\r\n\t\tfont-size: 18px;\r\n\t\tcolor: #909399;\r\n\t\tcursor: pointer;\r\n\t}\r\n\r\n\t.section-content {\r\n\t\tpadding: 15px;\r\n\t}\r\n\r\n\t/* AI配置区域 */\r\n\t.ai-grid {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tgap: 10px;\r\n\t}\r\n\r\n\t.ai-card {\r\n\t\twidth: calc(50% - 5px);\r\n\t\tborder: 1px solid #ebeef5;\r\n\t\tborder-radius: 8px;\r\n\t\tpadding: 10px;\r\n\t\ttransition: all 0.3s;\r\n\t\tmin-height: 65px;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.ai-card.ai-enabled {\r\n\t\tborder-color: #409EFF;\r\n\t\tbackground-color: #f0f8ff;\r\n\t}\r\n\r\n\t.ai-card.ai-disabled {\r\n\t\tbackground-color: #fafafa;\r\n\t\tborder-color: #e4e7ed;\r\n\t\tborder-style: dashed;\r\n\t\tpointer-events: none;\r\n\t}\r\n\r\n\t.ai-avatar.avatar-disabled {\r\n\t\topacity: 0.7;\r\n\t\tfilter: grayscale(30%);\r\n\t}\r\n\r\n\t.ai-name.name-disabled {\r\n\t\tcolor: #373839;\r\n\t}\r\n\r\n\t.login-required {\r\n\t\tfont-size: 9px;\r\n\t\tcolor: red;\r\n\t\tmargin-top: 2px;\r\n\t\tline-height: 1;\r\n\t}\r\n\r\n\t.loading-text {\r\n\t\tfont-size: 9px;\r\n\t\tcolor: #409EFF;\r\n\t\tmargin-top: 2px;\r\n\t\tline-height: 1;\r\n\t}\r\n\r\n\t.capability-tag.capability-disabled {\r\n\t\topacity: 0.5;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tborder-color: #e4e7ed;\r\n\t\tpointer-events: none;\r\n\t}\r\n\r\n\t.capability-tag.capability-disabled .capability-text {\r\n\t\tcolor: #c0c4cc;\r\n\t}\r\n\r\n\t.ai-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-start;\r\n\t\tmargin-bottom: 8px;\r\n\t\tmin-height: 24px;\r\n\t}\r\n\r\n\t.ai-avatar {\r\n\t\twidth: 24px;\r\n\t\theight: 24px;\r\n\t\tborder-radius: 12px;\r\n\t\tmargin-right: 8px;\r\n\t}\r\n\r\n\t.ai-info {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.ai-name-container {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: flex-start;\r\n\t\tmin-width: 0;\r\n\t}\r\n\r\n\t.ai-name {\r\n\t\tfont-size: 12px;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #303133;\r\n\t\twhite-space: nowrap;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\tmax-width: 100%;\r\n\t}\r\n\r\n\t.ai-capabilities {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tgap: 4px;\r\n\t}\r\n\r\n\t.capability-tag {\r\n\t\tpadding: 2px 6px;\r\n\t\tborder-radius: 10px;\r\n\t\tborder: 1px solid #dcdfe6;\r\n\t\tbackground-color: #fff;\r\n\t\ttransition: all 0.3s;\r\n\t}\r\n\r\n\t.capability-tag.capability-active {\r\n\t\tbackground-color: #409EFF;\r\n\t\tborder-color: #409EFF;\r\n\t}\r\n\r\n\t.capability-text {\r\n\t\tfont-size: 10px;\r\n\t\tcolor: #606266;\r\n\t}\r\n\r\n\t.capability-tag.capability-active .capability-text {\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t/* 提示词输入区域 */\r\n\t.prompt-textarea {\r\n\t\twidth: 100%;\r\n\t\tmin-height: 80px;\r\n\t\tpadding: 10px;\r\n\t\tborder: 1px solid #dcdfe6;\r\n\t\tborder-radius: 4px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 1.5;\r\n\t\tresize: none;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.prompt-footer {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-top: 10px;\r\n\t}\r\n\r\n\t.word-count {\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #909399;\r\n\t}\r\n\r\n\t.send-btn {\r\n\t\tbackground-color: #409EFF;\r\n\t\tcolor: #fff;\r\n\t\tborder: none;\r\n\t\tborder-radius: 20px;\r\n\t\tpadding: 6px 0;\r\n\t\tfont-size: 14px;\r\n\t\twidth: 50%;\r\n\t\theight: 30px;\r\n\t\tdisplay: flex;\r\n\t\tmargin-left: 50%;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.send-btn-disabled {\r\n\t\tbackground-color: #c0c4cc;\r\n\t}\r\n\r\n\t/* 任务执行状态 */\r\n\t.task-flow {\r\n\t\tmargin-bottom: 15px;\r\n\t}\r\n\r\n\t.task-item {\r\n\t\tborder: 1px solid #ebeef5;\r\n\t\tborder-radius: 8px;\r\n\t\tmargin-bottom: 10px;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.task-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 12px;\r\n\t\tbackground-color: #fafafa;\r\n\t\tborder-bottom: 1px solid #ebeef5;\r\n\t}\r\n\r\n\t.task-left {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tgap: 8px;\r\n\t}\r\n\r\n\t.task-avatar {\r\n\t\twidth: 20px;\r\n\t\theight: 20px;\r\n\t\tborder-radius: 10px;\r\n\t}\r\n\r\n\t.task-name {\r\n\t\tfont-size: 14px;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #303133;\r\n\t}\r\n\r\n\t.task-right {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tgap: 5px;\r\n\t}\r\n\r\n\t.status-text {\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #606266;\r\n\t}\r\n\r\n\t.status-icon {\r\n\t\tfont-size: 14px;\r\n\t}\r\n\r\n\t.status-completed {\r\n\t\tcolor: #67c23a;\r\n\t}\r\n\r\n\t.status-failed {\r\n\t\tcolor: #f56c6c;\r\n\t}\r\n\r\n\t.status-running {\r\n\t\tcolor: #409EFF;\r\n\t\tanimation: rotate 1s linear infinite;\r\n\t}\r\n\r\n\t.status-idle {\r\n\t\tcolor: #909399;\r\n\t}\r\n\r\n\t@keyframes rotate {\r\n\t\tfrom {\r\n\t\t\ttransform: rotate(0deg);\r\n\t\t}\r\n\r\n\t\tto {\r\n\t\t\ttransform: rotate(360deg);\r\n\t\t}\r\n\t}\r\n\r\n\t/* 进度日志 */\r\n\t.progress-logs {\r\n\t\tpadding: 10px 15px;\r\n\t\tmax-height: 150px;\r\n\t\toverflow-y: auto;\r\n\t}\r\n\r\n\t.progress-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-start;\r\n\t\tmargin-bottom: 8px;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.progress-item:last-child {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\r\n\t.progress-dot {\r\n\t\twidth: 8px;\r\n\t\theight: 8px;\r\n\t\tborder-radius: 4px;\r\n\t\tbackground-color: #dcdfe6;\r\n\t\tmargin-right: 10px;\r\n\t\tmargin-top: 6px;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\r\n\t.progress-dot.dot-completed {\r\n\t\tbackground-color: #67c23a;\r\n\t}\r\n\r\n\t.progress-content {\r\n\t\tflex: 1;\r\n\t\tmin-width: 0;\r\n\t}\r\n\r\n\t.progress-time {\r\n\t\tfont-size: 10px;\r\n\t\tcolor: #909399;\r\n\t\tmargin-bottom: 2px;\r\n\t}\r\n\r\n\t.progress-text {\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #606266;\r\n\t\tline-height: 1.4;\r\n\t\tword-break: break-all;\r\n\t}\r\n\r\n\t/* 主机可视化 */\r\n\t.screenshots-section {\r\n\t\tmargin-top: 15px;\r\n\t}\r\n\r\n\t.screenshots-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 10px;\r\n\t\tgap: 10px;\r\n\t}\r\n\r\n\t.section-subtitle {\r\n\t\tfont-size: 14px;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #303133;\r\n\t}\r\n\r\n\t.auto-play-text {\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #606266;\r\n\t}\r\n\r\n\t.screenshots-swiper {\r\n\t\theight: 200px;\r\n\t\tborder-radius: 8px;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.screenshot-image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t/* 结果展示区域 - 简洁标签页风格 */\r\n\r\n\t.result-tabs {\r\n\t\twhite-space: nowrap;\r\n\t\tmargin-bottom: 20px;\r\n\t\tborder-bottom: 1px solid #ebeef5;\r\n\t}\r\n\r\n\t.tab-container {\r\n\t\tdisplay: flex;\r\n\t\tgap: 0;\r\n\t\tpadding: 0 15px;\r\n\t}\r\n\r\n\t.result-tab {\r\n\t\tflex-shrink: 0;\r\n\t\tpadding: 12px 20px;\r\n\t\tposition: relative;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tbackground: transparent;\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.result-tab::after {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\tleft: 50%;\r\n\t\twidth: 0;\r\n\t\theight: 2px;\r\n\t\tbackground: #409EFF;\r\n\t\ttransition: all 0.3s ease;\r\n\t\ttransform: translateX(-50%);\r\n\t}\r\n\r\n\t.result-tab.tab-active::after {\r\n\t\twidth: 80%;\r\n\t}\r\n\r\n\t.tab-text {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #909399;\r\n\t\tfont-weight: 400;\r\n\t\ttransition: all 0.3s ease;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\t.result-tab.tab-active .tab-text {\r\n\t\tcolor: #409EFF;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.result-tab:active {\r\n\t\ttransform: translateY(1px);\r\n\t}\r\n\r\n\t.result-content {\r\n\t\tmargin-top: 10px;\r\n\t}\r\n\r\n\t.result-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 10px;\r\n\t\tpadding-bottom: 8px;\r\n\t\tborder-bottom: 1px solid #ebeef5;\r\n\t}\r\n\r\n\t.result-title {\r\n\t\tfont-size: 14px;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #303133;\r\n\t}\r\n\r\n\r\n\r\n\t.result-body {\r\n\t\tmargin-bottom: 15px;\r\n\t}\r\n\r\n\t.result-image-container {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.result-image {\r\n\t\tmax-width: 100%;\r\n\t\tborder-radius: 8px;\r\n\t}\r\n\r\n\t/* PDF文件容器样式 */\r\n\t.result-pdf-container {\r\n\t\tbackground-color: #f9f9f9;\r\n\t\tborder-radius: 8px;\r\n\t\tborder: 2px dashed #dcdfe6;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.pdf-placeholder {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tpadding: 20px;\r\n\t}\r\n\r\n\t.pdf-icon {\r\n\t\tfont-size: 48px;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t.pdf-text {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #606266;\r\n\t\tmargin-bottom: 15px;\r\n\t}\r\n\r\n\t.pdf-actions {\r\n\t\tdisplay: flex;\r\n\t\tgap: 10px;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.pdf-btn {\r\n\t\tborder-radius: 4px;\r\n\t\tpadding: 8px 16px;\r\n\t\tfont-size: 12px;\r\n\t\theight: auto;\r\n\t\tline-height: 1.2;\r\n\t\tflex: 1;\r\n\t\tmax-width: 100px;\r\n\t}\r\n\r\n\t.download-btn {\r\n\t\tbackground-color: #f6ffed;\r\n\t\tcolor: #52c41a;\r\n\t\tborder: 1px solid #b7eb8f;\r\n\t}\r\n\r\n\t.copy-btn {\r\n\t\tbackground-color: #fff7e6;\r\n\t\tcolor: #fa8c16;\r\n\t\tborder: 1px solid #ffd591;\r\n\t}\r\n\r\n\t.result-text {\r\n\t\tpadding: 10px;\r\n\t\tbackground-color: #f9f9f9;\r\n\t\tborder-radius: 8px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 1.6;\r\n\t\tmax-height: 300px;\r\n\t\toverflow-y: auto;\r\n\t}\r\n\r\n\t.result-actions {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: flex-end;\r\n\t\tgap: 8px;\r\n\t\tflex-wrap: wrap;\r\n\t\tmargin-bottom: 15px;\r\n\t}\r\n\r\n\t.action-btn-small, .share-link-btn, .collect-btn {\r\n\t\tborder: 1px solid #dcdfe6;\r\n\t\tborder-radius: 12px;\r\n\t\tpadding: 4px 12px;\r\n\t\tfont-size: 12px;\r\n\t\theight: auto;\r\n\t\tline-height: 1.2;\r\n\t\tmin-width: 60px;\r\n\t\ttext-align: center;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.action-btn-small {\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tcolor: #606266;\r\n\t\tborder-color: #dcdfe6;\r\n\t}\r\n\r\n\t.share-link-btn {\r\n\t\tbackground-color: #67c23a;\r\n\t\tcolor: #fff;\r\n\t\tborder-color: #67c23a;\r\n\t}\r\n\r\n\t.collect-btn {\r\n\t\tbackground-color: #e6a23c;\r\n\t\tcolor: #fff;\r\n\t\tborder-color: #e6a23c;\r\n\t}\r\n\r\n\t/* 按钮悬停和点击效果 */\r\n\t.action-btn-small:active {\r\n\t\topacity: 0.8;\r\n\t\ttransform: scale(0.95);\r\n\t}\r\n\r\n\t.share-link-btn:active {\r\n\t\topacity: 0.8;\r\n\t\ttransform: scale(0.95);\r\n\t}\r\n\r\n\t.collect-btn:active {\r\n\t\topacity: 0.8;\r\n\t\ttransform: scale(0.95);\r\n\t}\r\n\r\n\t/* 智能评分按钮在标题栏 */\r\n\t.score-btn {\r\n\t\tbackground-color: #409EFF;\r\n\t\tcolor: #fff;\r\n\t\tborder: none;\r\n\t\tborder-radius: 12px;\r\n\t\tpadding: 4px 12px;\r\n\t\tfont-size: 12px;\r\n\t\theight: auto;\r\n\t\tline-height: 1.2;\r\n\t\tmargin-left: 57%;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\r\n\t/* 历史记录抽屉 */\r\n\t.drawer-mask {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tz-index: 999;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: flex-end;\r\n\t}\r\n\r\n\t.drawer-container {\r\n\t\twidth: 280px;\r\n\t\theight: 100vh;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.drawer-content {\r\n\t\tmargin-top: 120rpx;\r\n\t\theight: 100vh;\r\n\t\tbackground-color: #fff;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.drawer-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 15px;\r\n\t\tborder-bottom: 1px solid #ebeef5;\r\n\t}\r\n\r\n\t.drawer-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #303133;\r\n\t}\r\n\r\n\t.history-list {\r\n\t\tflex: 1;\r\n\t\tpadding: 10px;\r\n\t\theight: calc(100vh - 60px);\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.history-group {\r\n\t\tmargin-bottom: 15px;\r\n\t}\r\n\r\n\t.history-date {\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #909399;\r\n\t\tmargin-bottom: 8px;\r\n\t\tpadding: 5px 0;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.history-item {\r\n\t\tbackground-color: #f9f9f9;\r\n\t\tborder-radius: 8px;\r\n\t\tpadding: 10px;\r\n\t\tmargin-bottom: 8px;\r\n\t}\r\n\r\n\t.history-prompt {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #303133;\r\n\t\tline-height: 1.4;\r\n\t\tmargin-bottom: 5px;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.history-time {\r\n\t\tfont-size: 10px;\r\n\t\tcolor: #909399;\r\n\t}\r\n\r\n\t/* 智能评分弹窗 */\r\n\t.popup-mask {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tz-index: 999;\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-end;\r\n\t}\r\n\r\n\t.score-modal {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 20px 20px 0 0;\r\n\t\tmax-height: 80vh;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.score-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 15px;\r\n\t\tborder-bottom: 1px solid #ebeef5;\r\n\t}\r\n\r\n\t.score-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #303133;\r\n\t}\r\n\r\n\t.score-content {\r\n\t\tflex: 1;\r\n\t\tpadding: 15px;\r\n\t\toverflow-y: auto;\r\n\t}\r\n\r\n\t.score-selection {\r\n\t\tmargin-bottom: 20px;\r\n\t}\r\n\r\n\t.score-subtitle {\r\n\t\tfont-size: 14px;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #303133;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t.score-checkboxes {\r\n\t\tmargin-top: 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 8px;\r\n\t}\r\n\r\n\t.checkbox-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tgap: 8px;\r\n\t}\r\n\r\n\t.checkbox-text {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #606266;\r\n\t}\r\n\r\n\t.score-prompt-section {\r\n\t\tmargin-bottom: 20px;\r\n\t}\r\n\r\n\t.score-textarea {\r\n\t\twidth: 100%;\r\n\t\theight: 120px;\r\n\t\tpadding: 10px;\r\n\t\tborder: 1px solid #dcdfe6;\r\n\t\tborder-radius: 8px;\r\n\t\tfont-size: 14px;\r\n\t\tresize: none;\r\n\t\tbox-sizing: border-box;\r\n\t\tmargin-top: 10px;\r\n\t}\r\n\r\n\t.score-submit-btn {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #409EFF;\r\n\t\tcolor: #fff;\r\n\t\tborder: none;\r\n\t\tborder-radius: 8px;\r\n\t\tpadding: 12px;\r\n\t\tfont-size: 16px;\r\n\t}\r\n\r\n\t.score-submit-btn[disabled] {\r\n\t\tbackground-color: #c0c4cc;\r\n\t}\r\n\r\n\t/* 响应式布局 */\r\n\t@media (max-width: 375px) {\r\n\t\t.ai-card {\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\r\n\t\t.header-content {\r\n\t\t\tpadding: 8px 12px;\r\n\t\t}\r\n\r\n\t\t.section-block {\r\n\t\t\tmargin: 8px 12px;\r\n\t\t}\r\n\t}\r\n\r\n\t/* 响应式布局 */\r\n\t@media (max-width: 375px) {\r\n\t\t.ai-card {\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\r\n\t\t.header-content {\r\n\t\t\tpadding: 8px 12px;\r\n\t\t}\r\n\r\n\t\t.section-block {\r\n\t\t\tmargin: 8px 12px;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=51b5538d&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=51b5538d&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751816798326\n      var cssReload = require(\"D:/hbuiderx/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}