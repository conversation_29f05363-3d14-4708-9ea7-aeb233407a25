{"version": 3, "sources": ["webpack:////Users/<USER>/AGI/gitee/anal-xudu/uni_modules/lime-painter/components/l-painter-image/l-painter-image.vue?78b8", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/uni_modules/lime-painter/components/l-painter-image/l-painter-image.vue?3d6d", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/uni_modules/lime-painter/components/l-painter-image/l-painter-image.vue?0722", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/uni_modules/lime-painter/components/l-painter-image/l-painter-image.vue?fc1a", "uni-app:///uni_modules/lime-painter/components/l-painter-image/l-painter-image.vue"], "names": ["name", "mixins", "props", "id", "css", "src", "data", "type", "el"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACmE;AACL;;;AAG9D;AACsN;AACtN,gBAAgB,iNAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA22B,CAAgB,6yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACK/3B;;;;;eACA;EACAA;EACAC;EACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;QACAJ;QACAC;MACA;IACA;EACA;AACA;AAAA,2B", "file": "uni_modules/lime-painter/components/l-painter-image/l-painter-image.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./l-painter-image.vue?vue&type=template&id=6347946f&\"\nvar renderjs\nimport script from \"./l-painter-image.vue?vue&type=script&lang=js&\"\nexport * from \"./l-painter-image.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/lime-painter/components/l-painter-image/l-painter-image.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-painter-image.vue?vue&type=template&id=6347946f&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-painter-image.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-painter-image.vue?vue&type=script&lang=js&\"", "<template>\n\t\n</template>\n\n<script>\n\timport {parent, children} from '../common/relation';\n\texport default {\n\t\tname: 'lime-painter-image',\n\t\tmixins:[children('painter')],\n\t\tprops: {\n\t\t\tid: String,\n\t\t\tcss: [String, Object],\n\t\t\tsrc: String\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\ttype: 'image',\n\t\t\t\tel: {\n\t\t\t\t\tcss: {},\n\t\t\t\t\tsrc: null\n\t\t\t\t},\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n</style>\n"], "sourceRoot": ""}