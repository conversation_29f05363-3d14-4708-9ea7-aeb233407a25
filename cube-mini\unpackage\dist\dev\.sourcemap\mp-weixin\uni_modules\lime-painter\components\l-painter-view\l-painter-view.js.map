{"version": 3, "sources": ["webpack:////Users/<USER>/AGI/gitee/anal-xudu/uni_modules/lime-painter/components/l-painter-view/l-painter-view.vue?165a", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/uni_modules/lime-painter/components/l-painter-view/l-painter-view.vue?794b", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/uni_modules/lime-painter/components/l-painter-view/l-painter-view.vue?bd03", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/uni_modules/lime-painter/components/l-painter-view/l-painter-view.vue?8e58", "uni-app:///uni_modules/lime-painter/components/l-painter-view/l-painter-view.vue"], "names": ["name", "mixins", "props", "id", "type", "default", "css", "data", "el", "views", "mounted"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;;;AAG7D;AACsN;AACtN,gBAAgB,iNAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA02B,CAAgB,4yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACK93B;;;;;eACA;EACAA;EACAC;EACAC;IACAC;IACAC;MACAA;MACAC;IACA;IACAC;EACA;EACAC;IACA;MACA;MACAC;QACAF;QACAG;MACA;IACA;EACA;EACAC,6BAEA;AACA;AAAA,2B", "file": "uni_modules/lime-painter/components/l-painter-view/l-painter-view.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./l-painter-view.vue?vue&type=template&id=0c5815fd&\"\nvar renderjs\nimport script from \"./l-painter-view.vue?vue&type=script&lang=js&\"\nexport * from \"./l-painter-view.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/lime-painter/components/l-painter-view/l-painter-view.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-painter-view.vue?vue&type=template&id=0c5815fd&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-painter-view.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-painter-view.vue?vue&type=script&lang=js&\"", "<template>\n\t<view><slot/></view>\n</template>\n\n<script>\n\timport {parent, children} from '../common/relation';\n\texport default {\n\t\tname: 'lime-painter-view',\n\t\tmixins:[children('painter'), parent('painter')],\n\t\tprops: {\n\t\t\tid: String,\n\t\t\ttype: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'view'\n\t\t\t},\n\t\t\tcss: [String, Object],\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// type: 'view',\n\t\t\t\tel: {\n\t\t\t\t\tcss: {},\n\t\t\t\t\tviews:[]\n\t\t\t\t},\n\t\t\t}\n\t\t},\n\t\tmounted() {\n\t\t\t\n\t\t}\n\t}\n</script>\n\n<style>\n</style>\n"], "sourceRoot": ""}