{"version": 3, "sources": ["webpack:////Users/<USER>/AGI/gitee/anal-xudu/uni_modules/lime-painter/components/l-painter/l-painter.vue?9519", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/uni_modules/lime-painter/components/l-painter/l-painter.vue?ff25", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/uni_modules/lime-painter/components/l-painter/l-painter.vue?791b", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/uni_modules/lime-painter/components/l-painter/l-painter.vue?a01e", "uni-app:///uni_modules/lime-painter/components/l-painter/l-painter.vue", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/uni_modules/lime-painter/components/l-painter/l-painter.vue?6f90", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/uni_modules/lime-painter/components/l-painter/l-painter.vue?2bef"], "names": ["name", "mixins", "data", "use2dCanvas", "canvasHeight", "canvasWidth", "parentWidth", "inited", "progress", "firstRender", "done", "tasks", "computed", "styles", "canvasId", "size", "dpr", "boardWidth", "width", "boardHeight", "height", "hasBoard", "elements", "created", "mounted", "setTimeout", "deep", "immediate", "destroyed", "clearTimeout", "methods", "watchRender", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filePath", "param", "pathType", "getSize", "args", "canvasToTempFilePathSync", "runTask", "getParentWeith", "uni", "in", "select", "boundingClientRect", "exec", "resolve", "render", "Object", "console", "ctx", "canvas", "after<PERSON>elay", "Promise", "top", "left", "context", "pixelRatio", "useCORS", "createImage", "performance", "listen", "onProgress", "onEffectFail", "then", "catch", "draw", "node", "canvasDraw", "getContext", "type", "_getContext", "strLen", "len", "fontSize", "canvasToTempFilePath", "success", "tempFile<PERSON>ath", "result", "copyArgs", "id", "fileType", "quality", "o<PERSON>ile<PERSON><PERSON>", "reject"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACsN;AACtN,gBAAgB,iNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAq2B,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACqBz3B;AACA;AACA;AAGA;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AAAA,eAKA;EACAA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QAAA;QAAAC;MACA;MACA;IACA;IACAC;MACA;QAAA;QAAAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OACA;YAAA;cACA;gBACAC;kBACA;oBACAC;oBACAC;kBACA;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EAYAC;IACA;IACA;IACA;IACA;IACA;IACAC;EACA;EAEAC;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBACA;gBACA;gBACAF;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA,QACAC;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAF;gBAAA;gBAAA;cAAA;gBAAA,MACAE;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAF;cAAA;gBAEA;kBACA;gBACA;gBAAA,kCACAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,QACAC;gBAAA,QACAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA,MACAnB;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IACAoB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;IACA;IAEAC;MAAA;MACA;QACAC,0BACAC,WACAC,wBACAC,qBACAC;UACA;YAAA3B;YAAAE;UACA;UACA;UACA;UACA0B;QACA;MACA;IACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAV;gBAAA,IACAW;kBAAA;kBAAA;gBAAA;gBAAA,kCACAC;cAAA;gBAEA;gBACA;gBAAA;gBAAA,OAIA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBAGA/C,cAKA,OALAA,aACAc,aAIA,OAJAA,YACAE,cAGA,OAHAA,aACAgC,SAEA,OAFAA,QACAC,aACA,OADAA;gBAAA,MAEAjD;kBAAA;kBAAA;gBAAA;gBAAA,kCACAkD;cAAA;gBAEA;kBACAC;kBACAC;kBACArC;kBACAE;gBACA;gBACA;gBACA;kBAAA,QACAiB;kBAAA,QACAA;kBACA;oBACAW;sBAAA9B;oBAAA;kBACA;kBACAgB;oBACAsB;oBACAL;oBACAjC;oBACAE;oBACAqC;oBACAC;oBACAC;oBACAC;oBACAC;sBACAC;wBACA;wBACA;sBACA;sBACAC;wBACA;sBACA;oBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA7C;gBAAAE;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;kBAAA;gBAAA;cAAA;gBAAA,IACAjB;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA,MAEAiD;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACA;gBACA;kBACA,8BACAY;oBACA;kBACA,GACAC;oBACA;kBACA;gBACA;gBACA;gBAAA,kCACAZ;kBACAH;kBACAgB;kBACAC;gBACA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAKA;IACAC;MAAA;MAAA;MACA;QAAA;UAAA;YAAA;UAAA,UACAhB;QAAA;MAAA;IACA;IACAiB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;gBACApB;gBAAA,kCACAI;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA,kCACAA;cAAA;gBAEAiB;gBACAC;kBACA;oBACA9B,0BACAC,WACAC,oCACAC,qBACAC;sBACA;wBACA;wBACA;0BACA;0BACA;0BACA;wBACA;;wBAEA;wBACA;0BAAA,IACA2B;4BACA;4BACA;8BACA;gCACAC;8BACA;gCACAA;8BACA;4BACA;4BACA;0BACA;0BACAvB;4BACA;4BACA;4BACA;8BACAwB;4BACA;4BACAA;4BACA;8BACAxD;4BACA;0BACA;wBACA;wBAKA;wBACA4B;sBACA;wBACAG;sBACA;oBACA;kBACA;gBACA;gBAAA,IACA9C;kBAAA;kBAAA;gBAAA;gBAAA,kCACAoE;cAAA;gBAAA,kCAEA;kBACA9B,0BACAC,WACAC,oCACAwB,OACAtB;oBACA;sBAAAM;oBACA;sBACA;sBACA;wBACA;wBACA;wBACA;sBACA;sBACA;sBACAL;oBACA;sBACAG;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA0B;MAAA;MAAA;MACA;QAAA;UAAA;UAAA;YAAA;cAAA;gBAAA;kBACAxE;kBACAyE;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA;8BAAA;8BAAA,OAEA;4BAAA;8BAAAC;8BACAC;gCAAAD;8BAAA;8BACAxC;8BACAS;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAEA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAEA;oBAAA,gBATA8B;sBAAA;oBAAA;kBAAA;kBAAA,SAWA,uNACA;kBACA;kBAMAG;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACAjE;oBACAkE;oBACAC;oBACAC;kBACA;oBAAAN;kBAAA,IACA;kBACA;kBACA;kBAAA,KACAzE;oBAAA;oBAAA;kBAAA;kBACA4E;kBAAA;kBAGAI;kBAAA,KACA;oBAAA;oBAAA;kBAAA;kBACA1C;kBAAA;kBAAA;gBAAA;kBAAA;kBAAA,OAEA;gBAAA;kBAAAoC;kBACAxC;oBAAAwC;kBAAA;kBACA/B;oBAAA+B;kBAAA;gBAAA;kBAAA;kBAAA;gBAAA;kBAAA;kBAAA;kBAOAxC;kBACA+C;gBAAA;kBAAA;kBAAA;gBAAA;kBAaA3C;gBAAA;gBAAA;kBAAA;cAAA;YAAA;UAAA;QAAA,CAGA;QAAA;UAAA;QAAA;MAAA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AChcA;AAAA;AAAA;AAAA;AAA8tC,CAAgB,omCAAG,EAAC,C;;;;;;;;;;;ACAlvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/lime-painter/components/l-painter/l-painter.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./l-painter.vue?vue&type=template&id=cae877da&\"\nvar renderjs\nimport script from \"./l-painter.vue?vue&type=script&lang=js&\"\nexport * from \"./l-painter.vue?vue&type=script&lang=js&\"\nimport style0 from \"./l-painter.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/lime-painter/components/l-painter/l-painter.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-painter.vue?vue&type=template&id=cae877da&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-painter.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-painter.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"lime-painter\" ref=\"limepainter\">\n\t\t<view v-if=\"canvasId && size\" :style=\"styles\">\n\t\t\t<!-- #ifndef APP-NVUE -->\n\t\t\t<canvas class=\"lime-painter__canvas\" v-if=\"use2dCanvas\" :id=\"canvasId\" type=\"2d\" :style=\"size\"></canvas>\n\t\t\t<canvas class=\"lime-painter__canvas\" v-else :id=\"canvasId\" :canvas-id=\"canvasId\" :style=\"size\"\n\t\t\t\t:width=\"boardWidth * dpr\" :height=\"boardHeight * dpr\" :hidpi=\"hidpi\"></canvas>\n\n\t\t\t<!-- #endif -->\n\t\t\t<!-- #ifdef APP-NVUE -->\n\t\t\t<web-view :style=\"size\" ref=\"webview\"\n\t\t\t\tsrc=\"/uni_modules/lime-painter/hybrid/html/index.html\"\n\t\t\t\tclass=\"lime-painter__canvas\" @pagefinish=\"onPageFinish\" @error=\"onError\" @onPostMessage=\"onMessage\">\n\t\t\t</web-view>\n\t\t\t<!-- #endif -->\n\t\t</view>\n\t\t<slot />\n\t</view>\n</template>\n\n<script>\n\timport { parent } from '../common/relation'\n\timport props from './props'\n\timport {toPx, base64ToPath, pathToBase64, isBase64, sleep, getImageInfo }from './utils';\n\t//  #ifndef APP-NVUE\n\timport { canIUseCanvas2d, isPC} from './utils';\n\timport Painter from './painter';\n\t// import Painter from '@painter'\n\tconst nvue = {}\n\t//  #endif\n\t//  #ifdef APP-NVUE\n\timport nvue from './nvue'\n\t//  #endif\n\texport default {\n\t\tname: 'lime-painter',\n\t\tmixins: [props, parent('painter'), nvue],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tuse2dCanvas: false,\n\t\t\t\tcanvasHeight: 150,\n\t\t\t\tcanvasWidth: null,\n\t\t\t\tparentWidth: 0,\n\t\t\t\tinited: false,\n\t\t\t\tprogress: 0,\n\t\t\t\tfirstRender: 0,\n\t\t\t\tdone: false,\n\t\t\t\ttasks: []\n\t\t\t};\n\t\t},\n\t\tcomputed: {\n\t\t\tstyles() {\n\t\t\t\treturn `${this.size}${this.customStyle||''};` + (this.hidden && 'position: fixed; left: 1500rpx;')\n\t\t\t},\n\t\t\tcanvasId() {\n\t\t\t\treturn `l-painter${this._ && this._.uid || this._uid}`\n\t\t\t},\n\t\t\tsize() {\n\t\t\t\tif (this.boardWidth && this.boardHeight) {\n\t\t\t\t\treturn `width:${this.boardWidth}px; height: ${this.boardHeight}px;`;\n\t\t\t\t}\n\t\t\t},\n\t\t\tdpr() {\n\t\t\t\treturn this.pixelRatio || uni.getSystemInfoSync().pixelRatio;\n\t\t\t},\n\t\t\tboardWidth() {\n\t\t\t\tconst {width = 0} = (this.elements && this.elements.css) || this.elements || this\n\t\t\t\tconst w = toPx(width||this.width)\n\t\t\t\treturn w || Math.max(w, toPx(this.canvasWidth));\n\t\t\t},\n\t\t\tboardHeight() {\n\t\t\t\tconst {height = 0} = (this.elements && this.elements.css) || this.elements || this\n\t\t\t\tconst h = toPx(height||this.height)\n\t\t\t\treturn h || Math.max(h, toPx(this.canvasHeight));\n\t\t\t},\n\t\t\thasBoard() {\n\t\t\t\treturn this.board && Object.keys(this.board).length\n\t\t\t},\n\t\t\telements() {\n\t\t\t\treturn this.hasBoard ? this.board : JSON.parse(JSON.stringify(this.el))\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.use2dCanvas = this.type === '2d' && canIUseCanvas2d() && !isPC\n\t\t},\n\t\tasync mounted() {\n\t\t\tawait sleep(30)\n\t\t\tawait this.getParentWeith()\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.$watch('elements', this.watchRender, {\n\t\t\t\t\t\tdeep: true,\n\t\t\t\t\t\timmediate: true\n\t\t\t\t\t});\n\t\t\t\t}, 30)\n\t\t\t})\n\t\t},\n\t\t// #ifdef VUE3\n\t\tunmounted() {\n\t\t\tthis.done = false\n\t\t\tthis.inited = false\n\t\t\tthis.firstRender = 0\n\t\t\tthis.progress = 0\n\t\t\tthis.painter = null\n\t\t\tclearTimeout(this.rendertimer)\n\t\t},\n\t\t// #endif\n\t\t// #ifdef VUE2\n\t\tdestroyed() {\n\t\t\tthis.done = false\n\t\t\tthis.inited = false\n\t\t\tthis.firstRender = 0\n\t\t\tthis.progress = 0\n\t\t\tthis.painter = null\n\t\t\tclearTimeout(this.rendertimer)\n\t\t},\n\t\t// #endif\n\t\tmethods: {\n\t\t\tasync watchRender(val, old) {\n\t\t\t\tif (!val || !val.views || (!this.firstRender ? !val.views.length : !this.firstRender) || !Object.keys(val).length || JSON.stringify(val) == JSON.stringify(old)) return;\n\t\t\t\tthis.firstRender = 1\n\t\t\t\tthis.progress = 0\n\t\t\t\tthis.done = false\n\t\t\t\tclearTimeout(this.rendertimer)\n\t\t\t\tthis.rendertimer = setTimeout(() => {\n\t\t\t\t\tthis.render(val);\n\t\t\t\t}, this.beforeDelay)\n\t\t\t},\n\t\t\tasync setFilePath(path, param) {\n\t\t\t\tlet filePath = path\n\t\t\t\tconst {pathType = this.pathType} =  param || this\n\t\t\t\tif (pathType == 'base64' && !isBase64(path)) {\n\t\t\t\t\tfilePath = await pathToBase64(path)\n\t\t\t\t} else if (pathType == 'url' && isBase64(path)) {\n\t\t\t\t\tfilePath = await base64ToPath(path)\n\t\t\t\t}\n\t\t\t\tif (param && param.isEmit) {\n\t\t\t\t\tthis.$emit('success', filePath);\n\t\t\t\t}\n\t\t\t\treturn filePath\n\t\t\t},\n\t\t\tasync getSize(args) {\n\t\t\t\tconst {width} = args.css || args\n\t\t\t\tconst {height} = args.css || args\n\t\t\t\tif (!this.size) {\n\t\t\t\t\tif (width || height) {\n\t\t\t\t\t\tthis.canvasWidth = width || this.canvasWidth\n\t\t\t\t\t\tthis.canvasHeight = height || this.canvasHeight\n\t\t\t\t\t\tawait sleep(30);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tawait this.getParentWeith()\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tcanvasToTempFilePathSync(args) {\n\t\t\t\t// this.stopWatch && this.stopWatch()\n\t\t\t\t// this.stopWatch = this.$watch('done', (v) => {\n\t\t\t\t// \tif (v) {\n\t\t\t\t// \t\tthis.canvasToTempFilePath(args)\n\t\t\t\t// \t\tthis.stopWatch && this.stopWatch()\n\t\t\t\t// \t}\n\t\t\t\t// }, {\n\t\t\t\t// \timmediate: true\n\t\t\t\t// })\n\t\t\t\tthis.tasks.push(args)\n\t\t\t\tif(this.done){\n\t\t\t\t\tthis.runTask()\n\t\t\t\t}\n\t\t\t},\n\t\t\trunTask(){\n\t\t\t\twhile(this.tasks.length){\n\t\t\t\t\tconst task = this.tasks.shift()\t\n\t\t\t\t\t this.canvasToTempFilePath(task)\n\t\t\t\t}\n\t\t\t},\n\t\t\t// #ifndef APP-NVUE\n\t\t\tgetParentWeith() {\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\tuni.createSelectorQuery()\n\t\t\t\t\t\t.in(this)\n\t\t\t\t\t\t.select(`.lime-painter`)\n\t\t\t\t\t\t.boundingClientRect()\n\t\t\t\t\t\t.exec(res => {\n\t\t\t\t\t\t\tconst {width, height} = res[0]||{}\n\t\t\t\t\t\t\tthis.parentWidth = Math.ceil(width||0)\n\t\t\t\t\t\t\tthis.canvasWidth = this.parentWidth || 300\n\t\t\t\t\t\t\tthis.canvasHeight = height || this.canvasHeight||150\n\t\t\t\t\t\t\tresolve(res[0])\n\t\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t},\n\t\t\tasync render(args = {}) {\n\t\t\t\tif(!Object.keys(args).length) {\n\t\t\t\t\treturn console.error('空对象')\n\t\t\t\t}\n\t\t\t\tthis.progress = 0\n\t\t\t\tthis.done = false\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tthis.tempFilePath.length = 0\n\t\t\t\t// #endif\n\t\t\t\tawait this.getSize(args)\n\t\t\t\tconst ctx = await this.getContext();\n\t\t\t\t\n\t\t\t\tlet {\n\t\t\t\t\tuse2dCanvas,\n\t\t\t\t\tboardWidth,\n\t\t\t\t\tboardHeight,\n\t\t\t\t\tcanvas,\n\t\t\t\t\tafterDelay\n\t\t\t\t} = this;\n\t\t\t\tif (use2dCanvas && !canvas) {\n\t\t\t\t\treturn Promise.reject(new Error('canvas 没创建'));\n\t\t\t\t}\n\t\t\t\tthis.boundary = {\n\t\t\t\t\ttop: 0,\n\t\t\t\t\tleft: 0,\n\t\t\t\t\twidth: boardWidth,\n\t\t\t\t\theight: boardHeight\n\t\t\t\t};\n\t\t\t\tthis.painter = null\n\t\t\t\tif (!this.painter) {\n\t\t\t\t\tconst {width} = args.css || args\n\t\t\t\t\tconst {height} = args.css || args\n\t\t\t\t\tif(!width && this.parentWidth) {\n\t\t\t\t\t\tObject.assign(args, {width: this.parentWidth})\n\t\t\t\t\t}\n\t\t\t\t\tconst param = {\n\t\t\t\t\t\tcontext: ctx,\n\t\t\t\t\t\tcanvas,\n\t\t\t\t\t\twidth: boardWidth,\n\t\t\t\t\t\theight: boardHeight,\n\t\t\t\t\t\tpixelRatio: this.dpr,\n\t\t\t\t\t\tuseCORS: this.useCORS,\n\t\t\t\t\t\tcreateImage: getImageInfo.bind(this),\n\t\t\t\t\t\tperformance: this.performance,\n\t\t\t\t\t\tlisten: {\n\t\t\t\t\t\t\tonProgress: (v) => {\n\t\t\t\t\t\t\t\tthis.progress = v\n\t\t\t\t\t\t\t\tthis.$emit('progress', v)\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tonEffectFail: (err) => {\n\t\t\t\t\t\t\t\tthis.$emit('faill', err)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tthis.painter = new Painter(param)\n\t\t\t\t} \n\t\t\t\ttry{\n\t\t\t\t\t// vue3 赋值给data会引起图片无法绘制\n\t\t\t\t\tconst { width, height } = await this.painter.source(JSON.parse(JSON.stringify(args)))\n\t\t\t\t\tthis.boundary.height = this.canvasHeight = height\n\t\t\t\t\tthis.boundary.width = this.canvasWidth = width\n\t\t\t\t\tawait sleep(this.sleep);\n\t\t\t\t\tawait this.painter.render()\n\t\t\t\t\tawait new Promise(resolve => this.$nextTick(resolve));\n\t\t\t\t\tif (!use2dCanvas) {\n\t\t\t\t\t\tawait this.canvasDraw();\n\t\t\t\t\t}\n\t\t\t\t\tif (afterDelay && use2dCanvas) {\n\t\t\t\t\t\tawait sleep(afterDelay);\n\t\t\t\t\t}\n\t\t\t\t\tthis.$emit('done');\n\t\t\t\t\tthis.done = true\n\t\t\t\t\tif (this.isCanvasToTempFilePath) {\n\t\t\t\t\t\tthis.canvasToTempFilePath()\n\t\t\t\t\t\t\t.then(res => {\n\t\t\t\t\t\t\t\tthis.$emit('success', res.tempFilePath)\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\t\tthis.$emit('fail', new Error(JSON.stringify(err)));\n\t\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tthis.runTask()\n\t\t\t\t\treturn Promise.resolve({\n\t\t\t\t\t\tctx,\n\t\t\t\t\t\tdraw: this.painter,\n\t\t\t\t\t\tnode: this.node\n\t\t\t\t\t});\n\t\t\t\t}catch(e){\n\t\t\t\t\t//TODO handle the exception\n\t\t\t\t}\n\t\t\t\t\n\t\t\t},\n\t\t\tcanvasDraw(flag = false) {\n\t\t\t\treturn new Promise((resolve, reject) => this.ctx.draw(flag, () => setTimeout(() => resolve(), this\n\t\t\t\t\t.afterDelay)));\n\t\t\t},\n\t\t\tasync getContext() {\n\t\t\t\tif (!this.canvasWidth) {\n\t\t\t\t\tthis.$emit('fail', 'painter no size')\n\t\t\t\t\tconsole.error('[lime-painter]: 给画板或父级设置尺寸')\n\t\t\t\t\treturn Promise.reject();\n\t\t\t\t}\n\t\t\t\tif (this.ctx && this.inited) {\n\t\t\t\t\treturn Promise.resolve(this.ctx);\n\t\t\t\t}\n\t\t\t\tconst { type, use2dCanvas, dpr, boardWidth, boardHeight } = this;\n\t\t\t\tconst _getContext = () => {\n\t\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\t\tuni.createSelectorQuery()\n\t\t\t\t\t\t\t.in(this)\n\t\t\t\t\t\t\t.select(`#${this.canvasId}`)\n\t\t\t\t\t\t\t.boundingClientRect()\n\t\t\t\t\t\t\t.exec(res => {\n\t\t\t\t\t\t\t\tif (res) {\n\t\t\t\t\t\t\t\t\tconst ctx = uni.createCanvasContext(this.canvasId, this);\n\t\t\t\t\t\t\t\t\tif (!this.inited) {\n\t\t\t\t\t\t\t\t\t\tthis.inited = true;\n\t\t\t\t\t\t\t\t\t\tthis.use2dCanvas = false;\n\t\t\t\t\t\t\t\t\t\tthis.canvas = res;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 钉钉小程序框架不支持 measureText 方法，用此方法 mock\n\t\t\t\t\t\t\t\t\tif (!ctx.measureText) {\n\t\t\t\t\t\t\t\t\t\tfunction strLen(str) {\n\t\t\t\t\t\t\t\t\t\t\tlet len = 0;\n\t\t\t\t\t\t\t\t\t\t\tfor (let i = 0; i < str.length; i++) {\n\t\t\t\t\t\t\t\t\t\t\t\tif (str.charCodeAt(i) > 0 && str.charCodeAt(i) < 128) {\n\t\t\t\t\t\t\t\t\t\t\t\t\tlen++;\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\t\t\tlen += 2;\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\treturn len;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tctx.measureText = text => {\n\t\t\t\t\t\t\t\t\t\t\tlet fontSize = ctx.state && ctx.state.fontSize || 12;\n\t\t\t\t\t\t\t\t\t\t\tconst font = ctx.__font\n\t\t\t\t\t\t\t\t\t\t\tif (font && fontSize == 12) {\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize = parseInt(font.split(' ')[3], 10);\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\tfontSize /= 2;\n\t\t\t\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\t\t\t\twidth: strLen(text) * fontSize\n\t\t\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// #ifdef MP-ALIPAY\n\t\t\t\t\t\t\t\t\tctx.scale(dpr, dpr);\n\t\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t\t\tthis.ctx = ctx\n\t\t\t\t\t\t\t\t\tresolve(this.ctx);\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tconsole.error('[lime-painter] no node')\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t};\n\t\t\t\tif (!use2dCanvas) {\n\t\t\t\t\treturn _getContext();\n\t\t\t\t}\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\tuni.createSelectorQuery()\n\t\t\t\t\t\t.in(this)\n\t\t\t\t\t\t.select(`#${this.canvasId}`)\n\t\t\t\t\t\t.node()\n\t\t\t\t\t\t.exec(res => {\n\t\t\t\t\t\t\tlet {node: canvas} = res && res[0]||{};\n\t\t\t\t\t\t\tif(canvas) {\n\t\t\t\t\t\t\t\tconst ctx = canvas.getContext(type);\n\t\t\t\t\t\t\t\tif (!this.inited) {\n\t\t\t\t\t\t\t\t\tthis.inited = true;\n\t\t\t\t\t\t\t\t\tthis.use2dCanvas = true;\n\t\t\t\t\t\t\t\t\tthis.canvas = canvas;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tthis.ctx = ctx\n\t\t\t\t\t\t\t\tresolve(this.ctx);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tconsole.error('[lime-painter]: no size')\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\t\t\tcanvasToTempFilePath(args = {}) {\n\t\t\t\treturn new Promise(async (resolve, reject) => {\n\t\t\t\t\tconst { use2dCanvas, canvasId, dpr, fileType, quality } = this;\n\t\t\t\t\tconst success = async (res) => {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst tempFilePath = await this.setFilePath(res.tempFilePath || res, args)\n\t\t\t\t\t\t\tconst result = Object.assign(res, {tempFilePath})\n\t\t\t\t\t\t\targs.success && args.success(result)\n\t\t\t\t\t\t\tresolve(result)\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tthis.$emit('fail', e)\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tlet { top: y = 0, left: x = 0, width, height } = this.boundary || this;\n\t\t\t\t\t// let destWidth = width * dpr;\n\t\t\t\t\t// let destHeight = height * dpr;\n\t\t\t\t\t// #ifdef MP-ALIPAY\n\t\t\t\t\t// width = destWidth;\n\t\t\t\t\t// height = destHeight;\n\t\t\t\t\t// #endif\n\t\t\t\t\t\n\t\t\t\t\tconst copyArgs = Object.assign({\n\t\t\t\t\t\t// x,\n\t\t\t\t\t\t// y,\n\t\t\t\t\t\t// width,\n\t\t\t\t\t\t// height,\n\t\t\t\t\t\t// destWidth,\n\t\t\t\t\t\t// destHeight,\n\t\t\t\t\t\tcanvasId,\n\t\t\t\t\t\tid: canvasId,\n\t\t\t\t\t\tfileType,\n\t\t\t\t\t\tquality,\n\t\t\t\t\t}, args, {success});\n\t\t\t\t\t// if(this.isPC || use2dCanvas) {\n\t\t\t\t\t// \tcopyArgs.canvas = this.canvas\n\t\t\t\t\t// }\n\t\t\t\t\tif (use2dCanvas) {\n\t\t\t\t\t\tcopyArgs.canvas = this.canvas\n\t\t\t\t\t\ttry{\n\t\t\t\t\t\t\t// #ifndef MP-ALIPAY\n\t\t\t\t\t\t\tconst oFilePath = this.canvas.toDataURL(`image/${args.fileType||fileType}`.replace(/pg/, 'peg'), args.quality||quality)\n\t\t\t\t\t\t\tif(/data:,/.test(oFilePath)) {\n\t\t\t\t\t\t\t\tuni.canvasToTempFilePath(copyArgs, this);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tconst tempFilePath = await this.setFilePath(oFilePath, args)\n\t\t\t\t\t\t\t\targs.success && args.success({tempFilePath})\n\t\t\t\t\t\t\t\tresolve({tempFilePath})\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t// #ifdef MP-ALIPAY\n\t\t\t\t\t\t\tthis.canvas.toTempFilePath(copyArgs)\n\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t}catch(e){\n\t\t\t\t\t\t\targs.fail && args.fail(e)\n\t\t\t\t\t\t\treject(e)\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// #ifdef MP-ALIPAY\n\t\t\t\t\t\tif(this.ctx.toTempFilePath) {\n\t\t\t\t\t\t\t// 钉钉\n\t\t\t\t\t\t\tconst ctx = uni.createCanvasContext(canvasId);\n\t\t\t\t\t\t\tctx.toTempFilePath(copyArgs);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tmy.canvasToTempFilePath(copyArgs);\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t// #ifndef MP-ALIPAY\n\t\t\t\t\t\tuni.canvasToTempFilePath(copyArgs, this);\n\t\t\t\t\t\t// #endif\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t\t// #endif\n\t\t}\n\t};\n</script>\n<style>\n\t.lime-painter,\n\t.lime-painter__canvas {\n\t\t// #ifndef APP-NVUE\n\t\twidth: 100%;\n\t\t// #endif\n\t\t// #ifdef APP-NVUE\n\t\tflex: 1;\n\t\t// #endif\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-painter.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-painter.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1737420404817\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}