{"version": 3, "sources": ["webpack:////Users/<USER>/AGI/gitee/anal-xudu/uni_modules/uni-card/components/uni-card/uni-card.vue?521f", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/uni_modules/uni-card/components/uni-card/uni-card.vue?6632", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/uni_modules/uni-card/components/uni-card/uni-card.vue?011b", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/uni_modules/uni-card/components/uni-card/uni-card.vue?9c58", "uni-app:///uni_modules/uni-card/components/uni-card/uni-card.vue", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/uni_modules/uni-card/components/uni-card/uni-card.vue?d715", "webpack:////Users/<USER>/AGI/gitee/anal-xudu/uni_modules/uni-card/components/uni-card/uni-card.vue?7d8c"], "names": ["name", "emits", "props", "title", "type", "default", "subTitle", "padding", "margin", "spacing", "extra", "cover", "thumbnail", "isFull", "is<PERSON><PERSON>ow", "shadow", "border", "methods", "onClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AACsN;AACtN,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAo2B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsCx3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA,eAkBA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACA;MACAT;MACAC;IACA;IACAS;MACA;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;EACA;EACAY;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACpHA;AAAA;AAAA;AAAA;AAA2lD,CAAgB,q6CAAG,EAAC,C;;;;;;;;;;;ACA/mD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-card/components/uni-card/uni-card.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-card.vue?vue&type=template&id=19622063&\"\nvar renderjs\nimport script from \"./uni-card.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-card.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-card.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-card/components/uni-card/uni-card.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-card.vue?vue&type=template&id=19622063&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-card.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-card.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"uni-card\" :class=\"{ 'uni-card--full': isFull, 'uni-card--shadow': isShadow,'uni-card--border':border}\"\n\t\t:style=\"{'margin':isFull?0:margin,'padding':spacing,'box-shadow':isShadow?shadow:''}\">\n\t\t<!-- 封面 -->\n\t\t<slot name=\"cover\">\n\t\t\t<view v-if=\"cover\" class=\"uni-card__cover\">\n\t\t\t\t<image class=\"uni-card__cover-image\" mode=\"widthFix\" @click=\"onClick('cover')\" :src=\"cover\"></image>\n\t\t\t</view>\n\t\t</slot>\n\t\t<slot name=\"title\">\n\t\t\t<view v-if=\"title || extra\" class=\"uni-card__header\">\n\t\t\t\t<!-- 卡片标题 -->\n\t\t\t\t<view class=\"uni-card__header-box\" @click=\"onClick('title')\">\n\t\t\t\t\t<view v-if=\"thumbnail\" class=\"uni-card__header-avatar\">\n\t\t\t\t\t\t<image class=\"uni-card__header-avatar-image\" :src=\"thumbnail\" mode=\"aspectFit\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"uni-card__header-content\">\n\t\t\t\t\t\t<text class=\"uni-card__header-content-title uni-ellipsis\">{{ title }}</text>\n\t\t\t\t\t\t<text v-if=\"title&&subTitle\"\n\t\t\t\t\t\t\tclass=\"uni-card__header-content-subtitle uni-ellipsis\">{{ subTitle }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-card__header-extra\" @click=\"onClick('extra')\">\n\t\t\t\t\t<text class=\"uni-card__header-extra-text\">{{ extra }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</slot>\n\t\t<!-- 卡片内容 -->\n\t\t<view class=\"uni-card__content\" :style=\"{padding:padding}\" @click=\"onClick('content')\">\n\t\t\t<slot></slot>\n\t\t</view>\n\t\t<view class=\"uni-card__actions\" @click=\"onClick('actions')\">\n\t\t\t<slot name=\"actions\"></slot>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\t/**\n\t * Card 卡片\n\t * @description 卡片视图组件\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=22\n\t * @property {String} title 标题文字\n\t * @property {String} subTitle 副标题\n\t * @property {Number} padding 内容内边距\n\t * @property {Number} margin 卡片外边距\n\t * @property {Number} spacing 卡片内边距\n\t * @property {String} extra 标题额外信息\n\t * @property {String} cover 封面图（本地路径需要引入）\n\t * @property {String} thumbnail 标题左侧缩略图\n\t * @property {Boolean} is-full = [true | false] 卡片内容是否通栏，为 true 时将去除padding值\n\t * @property {Boolean} is-shadow = [true | false] 卡片内容是否开启阴影\n\t * @property {String} shadow 卡片阴影\n\t * @property {Boolean} border 卡片边框\n\t * @event {Function} click 点击 Card 触发事件\n\t */\n\texport default {\n\t\tname: 'UniCard',\n\t\temits: ['click'],\n\t\tprops: {\n\t\t\ttitle: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\tsubTitle: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\tpadding: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '10px'\n\t\t\t},\n\t\t\tmargin: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '15px'\n\t\t\t},\n\t\t\tspacing: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '0 10px'\n\t\t\t},\n\t\t\textra: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\tcover: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\tthumbnail: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\tisFull: {\n\t\t\t\t// 内容区域是否通栏\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\tisShadow: {\n\t\t\t\t// 是否开启阴影\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\tshadow: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '0px 0px 3px 1px rgba(0, 0, 0, 0.08)'\n\t\t\t},\n\t\t\tborder: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tonClick(type) {\n\t\t\t\tthis.$emit('click', type)\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t$uni-border-3: #EBEEF5 !default;\n\t$uni-shadow-base:0 0px 6px 1px rgba($color: #a5a5a5, $alpha: 0.2) !default;\n\t$uni-main-color: #3a3a3a !default;\n\t$uni-base-color: #6a6a6a !default;\n\t$uni-secondary-color: #909399 !default;\n\t$uni-spacing-sm: 8px !default;\n\t$uni-border-color:$uni-border-3;\n\t$uni-shadow: $uni-shadow-base;\n\t$uni-card-title: 15px;\n\t$uni-cart-title-color:$uni-main-color;\n\t$uni-card-subtitle: 12px;\n\t$uni-cart-subtitle-color:$uni-secondary-color;\n\t$uni-card-spacing: 10px;\n\t$uni-card-content-color: $uni-base-color;\n\n\t.uni-card {\n\t\tmargin: $uni-card-spacing;\n\t\tpadding: 0 $uni-spacing-sm;\n\t\tborder-radius: 4px;\n\t\toverflow: hidden;\n\t\tfont-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, SimSun, sans-serif;\n\t\tbackground-color: #fff;\n\t\tflex: 1;\n\n\t\t.uni-card__cover {\n\t\t\tposition: relative;\n\t\t\tmargin-top: $uni-card-spacing;\n\t\t\tflex-direction: row;\n\t\t\toverflow: hidden;\n\t\t\tborder-radius: 4px;\n\t\t\t.uni-card__cover-image {\n\t\t\t\tflex: 1;\n\t\t\t\t// width: 100%;\n\t\t\t\t/* #ifndef APP-PLUS */\n\t\t\t\tvertical-align: middle;\n\t\t\t\t/* #endif */\n\t\t\t}\n\t\t}\n\n\t\t.uni-card__header {\n\t\t\tdisplay: flex;\n\t\t\tborder-bottom: 1px $uni-border-color solid;\n\t\t\tflex-direction: row;\n\t\t\talign-items: center;\n\t\t\tpadding: $uni-card-spacing;\n\t\t\toverflow: hidden;\n\n\t\t\t.uni-card__header-box {\n\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\tdisplay: flex;\n\t\t\t\t/* #endif */\n\t\t\t\tflex: 1;\n\t\t\t\tflex-direction: row;\n\t\t\t\talign-items: center;\n\t\t\t\toverflow: hidden;\n\t\t\t}\n\n\t\t\t.uni-card__header-avatar {\n\t\t\t\twidth: 40px;\n\t\t\t\theight: 40px;\n\t\t\t\toverflow: hidden;\n\t\t\t\tborder-radius: 5px;\n\t\t\t\tmargin-right: $uni-card-spacing;\n\t\t\t\t.uni-card__header-avatar-image {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\twidth: 40px;\n\t\t\t\t\theight: 40px;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.uni-card__header-content {\n\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\tdisplay: flex;\n\t\t\t\t/* #endif */\n\t\t\t\tflex-direction: column;\n\t\t\t\tjustify-content: center;\n\t\t\t\tflex: 1;\n\t\t\t\t// height: 40px;\n\t\t\t\toverflow: hidden;\n\n\t\t\t\t.uni-card__header-content-title {\n\t\t\t\t\tfont-size: $uni-card-title;\n\t\t\t\t\tcolor: $uni-cart-title-color;\n\t\t\t\t\t// line-height: 22px;\n\t\t\t\t}\n\n\t\t\t\t.uni-card__header-content-subtitle {\n\t\t\t\t\tfont-size: $uni-card-subtitle;\n\t\t\t\t\tmargin-top: 5px;\n\t\t\t\t\tcolor: $uni-cart-subtitle-color;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.uni-card__header-extra {\n\t\t\t\tline-height: 12px;\n\n\t\t\t\t.uni-card__header-extra-text {\n\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\tcolor: $uni-cart-subtitle-color;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.uni-card__content {\n\t\t\tpadding: $uni-card-spacing;\n\t\t\tfont-size: 14px;\n\t\t\tcolor: $uni-card-content-color;\n\t\t\tline-height: 22px;\n\t\t}\n\n\t\t.uni-card__actions {\n\t\t\tfont-size: 12px;\n\t\t}\n\t}\n\n\t.uni-card--border {\n\t\tborder: 1px solid $uni-border-color;\n\t}\n\n\t.uni-card--shadow {\n\t\tposition: relative;\n\t\t/* #ifndef APP-NVUE */\n\t\tbox-shadow: $uni-shadow;\n\t\t/* #endif */\n\t}\n\n\t.uni-card--full {\n\t\tmargin: 0;\n\t\tborder-left-width: 0;\n\t\tborder-left-width: 0;\n\t\tborder-radius: 0;\n\t}\n\n\t/* #ifndef APP-NVUE */\n\t.uni-card--full:after {\n\t\tborder-radius: 0;\n\t}\n\n\t/* #endif */\n\t.uni-ellipsis {\n\t\t/* #ifndef APP-NVUE */\n\t\toverflow: hidden;\n\t\twhite-space: nowrap;\n\t\ttext-overflow: ellipsis;\n\t\t/* #endif */\n\t\t/* #ifdef APP-NVUE */\n\t\tlines: 1;\n\t\t/* #endif */\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-card.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-card.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1737420406066\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}