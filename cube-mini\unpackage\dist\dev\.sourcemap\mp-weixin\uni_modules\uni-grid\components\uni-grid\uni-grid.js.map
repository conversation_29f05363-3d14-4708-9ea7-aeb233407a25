{"version": 3, "sources": ["webpack:////Users/<USER>/AGI/github/anal-xudu/uni_modules/uni-grid/components/uni-grid/uni-grid.vue?eda5", "webpack:////Users/<USER>/AGI/github/anal-xudu/uni_modules/uni-grid/components/uni-grid/uni-grid.vue?07c6", "webpack:////Users/<USER>/AGI/github/anal-xudu/uni_modules/uni-grid/components/uni-grid/uni-grid.vue?f934", "webpack:////Users/<USER>/AGI/github/anal-xudu/uni_modules/uni-grid/components/uni-grid/uni-grid.vue?7ff9", "uni-app:///uni_modules/uni-grid/components/uni-grid/uni-grid.vue", "webpack:////Users/<USER>/AGI/github/anal-xudu/uni_modules/uni-grid/components/uni-grid/uni-grid.vue?d117"], "names": ["renderjs", "component", "options", "__file", "components", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "recyclableRender", "staticRenderFns", "_withStripped", "name", "emits", "props", "column", "type", "default", "showBorder", "borderColor", "square", "highlight", "provide", "grid", "data", "elId", "width", "created", "mounted", "methods", "init", "setTimeout", "item", "change", "_getSize", "uni", "in", "select", "boundingClientRect", "exec", "fn"], "mappings": "kJAAA,oIACIA,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,KACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,wDACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,IAEjBC,GAAmB,EACnBC,EAAkB,GACtBR,EAAOS,eAAgB,G,iCCRvB,yHAAo3B,eAAG,G,gHCav3B,MAWA,CACAC,eACAC,iBACAC,OAEAC,QACAC,YACAC,WAGAC,YACAF,aACAC,YAGAE,aACAH,YACAC,mBAGAG,QACAJ,aACAC,YAEAI,WACAL,aACAC,aAGAK,mBACA,OACAC,YAGAC,gBACA,+DACA,OACAC,OACAC,UAGAC,mBACA,kBAEAC,mBAAA,WACA,2BACA,aAGAC,SACAC,gBAAA,WACAC,uBACA,wBACA,kCACAC,kBAGA,KAEAC,mBACA,wBAEAC,qBAAA,WAEAC,wBACAC,SACAC,8BACAC,qBACAC,kBACA,+CACAC,iBAWA,c,6DCzGA,yHAA2mD,eAAG,G", "file": "uni_modules/uni-grid/components/uni-grid/uni-grid.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-grid.vue?vue&type=template&id=aaae28a6&\"\nvar renderjs\nimport script from \"./uni-grid.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-grid.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-grid.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-grid/components/uni-grid/uni-grid.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-grid.vue?vue&type=template&id=aaae28a6&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-grid.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-grid.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-grid-wrap\">\r\n\t\t<view :id=\"elId\" ref=\"uni-grid\" class=\"uni-grid\" :class=\"{ 'uni-grid--border': showBorder }\" :style=\"{ 'border-left-color':borderColor}\">\r\n\t\t\t<slot />\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// #ifdef APP-NVUE\r\n\tconst dom = uni.requireNativePlugin('dom');\r\n\t// #endif\r\n\r\n\t/**\r\n\t * Grid 宫格\r\n\t * @description 宫格组件\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=27\r\n\t * @property {Number} column 每列显示个数\r\n\t * @property {String} borderColor 边框颜色\r\n\t * @property {Boolean} showBorder 是否显示边框\r\n\t * @property {Boolean} square 是否方形显示\r\n\t * @property {Boolean} Boolean 点击背景是否高亮\r\n\t * @event {Function} change 点击 grid 触发，e={detail:{index:0}}，index 为当前点击 gird 下标\r\n\t */\r\n\texport default {\r\n\t\tname: 'UniGrid',\r\n\t\temits:['change'],\r\n\t\tprops: {\r\n\t\t\t// 每列显示个数\r\n\t\t\tcolumn: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 3\r\n\t\t\t},\r\n\t\t\t// 是否显示边框\r\n\t\t\tshowBorder: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 边框颜色\r\n\t\t\tborderColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#D2D2D2'\r\n\t\t\t},\r\n\t\t\t// 是否正方形显示,默认为 true\r\n\t\t\tsquare: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\thighlight: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tprovide() {\r\n\t\t\treturn {\r\n\t\t\t\tgrid: this\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\tconst elId = `Uni_${Math.ceil(Math.random() * 10e5).toString(36)}`\r\n\t\t\treturn {\r\n\t\t\t\telId,\r\n\t\t\t\twidth: 0\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.children = []\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.$nextTick(()=>{\r\n\t\t\t\tthis.init()\r\n\t\t\t})\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinit() {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis._getSize((width) => {\r\n\t\t\t\t\t\tthis.children.forEach((item, index) => {\r\n\t\t\t\t\t\t\titem.width = width\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t}, 50)\r\n\t\t\t},\r\n\t\t\tchange(e) {\r\n\t\t\t\tthis.$emit('change', e)\r\n\t\t\t},\r\n\t\t\t_getSize(fn) {\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tuni.createSelectorQuery()\r\n\t\t\t\t\t.in(this)\r\n\t\t\t\t\t.select(`#${this.elId}`)\r\n\t\t\t\t\t.boundingClientRect()\r\n\t\t\t\t\t.exec(ret => {\r\n\t\t\t\t\t\tthis.width = parseInt((ret[0].width - 1) / this.column) + 'px'\r\n\t\t\t\t\t\tfn(this.width)\r\n\t\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tdom.getComponentRect(this.$refs['uni-grid'], (ret) => {\r\n\t\t\t\t\tthis.width = parseInt((ret.size.width - 1) / this.column) + 'px'\r\n\t\t\t\t\tfn(this.width)\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" >\r\n\t.uni-grid-wrap {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex: 1;\r\n\t\tflex-direction: column;\r\n\t\t/* #ifdef H5 */\r\n\t\twidth: 100%;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-grid {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\t// flex: 1;\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\r\n\t.uni-grid--border {\r\n\t\tposition: relative;\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tborder-left-color: #D2D2D2;\r\n\t\tborder-left-style: solid;\r\n\t\tborder-left-width: 0.5px;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tz-index: 1;\r\n\t\tborder-left: 1px #D2D2D2 solid;\r\n\t\t/* #endif */\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-grid.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-grid.vue?vue&type=style&index=0&lang=scss&\""], "sourceRoot": ""}