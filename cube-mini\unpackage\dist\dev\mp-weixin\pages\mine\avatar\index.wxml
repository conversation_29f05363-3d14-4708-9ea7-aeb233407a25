<view class="container"><view class="page-body uni-content-info"><view class="cropper-content"><block wx:if="{{isShowImg}}"><view class="uni-corpper" style="{{('width:'+cropperInitW+'px;height:'+cropperInitH+'px;background:#000')}}"><view class="uni-corpper-content" style="{{('width:'+cropperW+'px;height:'+cropperH+'px;left:'+cropperL+'px;top:'+cropperT+'px')}}"><image style="{{('width:'+cropperW+'px;height:'+cropperH+'px')}}" src="{{imageSrc}}"></image><view data-event-opts="{{[['touchstart',[['contentStartMove',['$event']]]],['touchmove',[['contentMoveing',['$event']]]],['touchend',[['contentTouchEnd',['$event']]]]]}}" class="uni-corpper-crop-box" style="{{('left:'+cutL+'px;top:'+cutT+'px;right:'+cutR+'px;bottom:'+cutB+'px')}}" catchtouchstart="__e" catchtouchmove="__e" catchtouchend="__e"><view class="uni-cropper-view-box"><view class="uni-cropper-dashed-h"></view><view class="uni-cropper-dashed-v"></view><view class="uni-cropper-line-t" data-drag="top" data-event-opts="{{[['touchstart',[['dragStart',['$event']]]],['touchmove',[['dragMove',['$event']]]]]}}" catchtouchstart="__e" catchtouchmove="__e"></view><view class="uni-cropper-line-r" data-drag="right" data-event-opts="{{[['touchstart',[['dragStart',['$event']]]],['touchmove',[['dragMove',['$event']]]]]}}" catchtouchstart="__e" catchtouchmove="__e"></view><view class="uni-cropper-line-b" data-drag="bottom" data-event-opts="{{[['touchstart',[['dragStart',['$event']]]],['touchmove',[['dragMove',['$event']]]]]}}" catchtouchstart="__e" catchtouchmove="__e"></view><view class="uni-cropper-line-l" data-drag="left" data-event-opts="{{[['touchstart',[['dragStart',['$event']]]],['touchmove',[['dragMove',['$event']]]]]}}" catchtouchstart="__e" catchtouchmove="__e"></view><view class="uni-cropper-point point-t" data-drag="top" data-event-opts="{{[['touchstart',[['dragStart',['$event']]]],['touchmove',[['dragMove',['$event']]]]]}}" catchtouchstart="__e" catchtouchmove="__e"></view><view class="uni-cropper-point point-tr" data-drag="topTight"></view><view class="uni-cropper-point point-r" data-drag="right" data-event-opts="{{[['touchstart',[['dragStart',['$event']]]],['touchmove',[['dragMove',['$event']]]]]}}" catchtouchstart="__e" catchtouchmove="__e"></view><view class="uni-cropper-point point-rb" data-drag="rightBottom" data-event-opts="{{[['touchstart',[['dragStart',['$event']]]],['touchmove',[['dragMove',['$event']]]]]}}" catchtouchstart="__e" catchtouchmove="__e"></view><view class="uni-cropper-point point-b" data-drag="bottom" data-event-opts="{{[['touchstart',[['dragStart',['$event']]]],['touchmove',[['dragMove',['$event']]]],['touchend',[['dragEnd',['$event']]]]]}}" catchtouchstart="__e" catchtouchmove="__e" catchtouchend="__e"></view><view class="uni-cropper-point point-bl" data-drag="bottomLeft"></view><view class="uni-cropper-point point-l" data-drag="left" data-event-opts="{{[['touchstart',[['dragStart',['$event']]]],['touchmove',[['dragMove',['$event']]]]]}}" catchtouchstart="__e" catchtouchmove="__e"></view><view class="uni-cropper-point point-lt" data-drag="leftTop"></view></view></view></view></view></block></view><view class="cropper-config"><button style="margin-top:30rpx;" type="primary reverse" data-event-opts="{{[['tap',[['getImage',['$event']]]]]}}" bindtap="__e">选择头像</button><button style="margin-top:30rpx;" type="warn" data-event-opts="{{[['tap',[['getImageInfo',['$event']]]]]}}" bindtap="__e">提交</button></view><canvas style="{{('position:absolute;border: 1px solid red; width:'+imageW+'px;height:'+imageH+'px;top:-9999px;left:-9999px;')}}" canvas-id="myCanvas"></canvas></view></view>