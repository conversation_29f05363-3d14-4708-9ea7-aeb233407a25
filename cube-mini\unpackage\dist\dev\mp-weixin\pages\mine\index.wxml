<view class="mine-container" style="{{'height:'+(windowHeight+'px')+';'}}"><view class="header-section"><view class="flex justify-between" style="padding:0 30rpx 50rpx;"><view class="flex align-center"><block wx:if="{{avatar}}"><image class="cu-avatar xl round" src="{{avatar}}" mode="widthFix"></image></block><block wx:if="{{!name}}"><view data-event-opts="{{[['tap',[['wxhandleLogin',['$event']]]]]}}" class="login-tip" bindtap="__e">点击登录</view></block><block wx:if="{{name}}"><view class="user-info"><view class="u_title">{{''+name+''}}</view><view class="u_title1">{{'用户ID: '+userid+''}}</view></view></block></view></view></view><view class="content-section"><view class="mine-actions grid col-4 text-center"><view data-event-opts="{{[['tap',[['myPoints',['$event']]]]]}}" class="action-item" bindtap="__e"><text style="font-size:16px;">{{''+points+''}}</text><text class="text">我的积分</text></view></view><view class="menu-list"><view data-event-opts="{{[['tap',[['handleToEditInfo',['$event']]]]]}}" class="list-cell list-cell-arrow" bindtap="__e"><view class="menu-item-box"><view class="iconfont icon-user menu-icon"></view><view>完善资料</view></view></view></view><block wx:if="{{name}}"><view class="action-btn"><button data-event-opts="{{[['tap',[['handleLogout',['$event']]]]]}}" class="login-btn cu-btn block bg-green lg round" bindtap="__e">退出登录</button></view></block></view></view>