<view class="container"><view class="example"><uni-forms class="vue-ref" vue-id="3fe9d4be-1" model="{{user}}" labelWidth="80px" rules="{{rules}}" data-ref="form" bind:__l="__l" vue-slots="{{['default']}}"><button class="avatar-wrapper" open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['onChooseAvatar',['$event']]]]]}}" bindchooseavatar="__e"><block wx:if="{{user.avatar}}"><image class="cu-avatar xl round" src="{{user.avatar}}" mode="widthFix"></image></block><block wx:if="{{!user.avatar}}"><text class="loginLogoText">授权头像</text></block></button><uni-forms-item vue-id="{{('3fe9d4be-2')+','+('3fe9d4be-1')}}" label="用户昵称" name="nickName" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('3fe9d4be-3')+','+('3fe9d4be-2')}}" type="nickname" placeholder="请输入昵称" value="{{user.nickName}}" data-event-opts="{{[['^input',[['__set_model',['$0','nickName','$event',[]],['user']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item></uni-forms><button class="login-btn cu-btn block bg-green lg round" type="primary" data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" bindtap="__e">提交</button></view></view>