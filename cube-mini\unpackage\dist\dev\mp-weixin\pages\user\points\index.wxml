<view style="{{'height:'+(windowHeight+'px')+';'}}"><view><view class="top"><view class="filter-box"><picker mode="date" value="{{selectedDateRange.start}}" start="{{minDate}}" end="{{maxDate}}" data-event-opts="{{[['change',[['onStartDateChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{''+selectedDateRange.start+' 至'}}</view></picker><picker mode="date" value="{{selectedDateRange.end}}" start="{{selectedDateRange.start}}" end="{{maxDate}}" data-event-opts="{{[['change',[['onEndDateChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{''+selectedDateRange.end+''}}</view></picker></view><view class="tab-nav"><block wx:for="{{tabItems}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onTabItemClick',[index]]]]]}}" class="{{['tab-item',(selectedIndex===index)?'active':'']}}" bindtap="__e">{{''+item.name+''}}</view></block></view></view><scroll-view class="list" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{triggered}}" data-event-opts="{{[['scrolltolower',[['onScrollToLower',['$event']]]],['refresherpulling',[['onPulling',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherpulling="__e" bindrefresherrefresh="__e"><block wx:for="{{listItems}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="list-item"><view class="point-text" style="display:flex;justify-content:space-between;"><text>{{item.change_type}}</text><text style="{{'color:'+(item.change_amount>=0?'green':'red')+';'+('font-size:'+('35rpx')+';')}}">{{item.change_amount}}</text></view><view class="point-text"><text style="color:#8f8f94;">{{item.create_time}}</text></view></view></block><block wx:if="{{!hasMore}}"><view class="end-text">已经到底啦~</view></block></scroll-view></view></view>