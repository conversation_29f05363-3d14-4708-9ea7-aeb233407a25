<view class="console-container data-v-51b5538d"><view class="header-fixed data-v-51b5538d"><view class="header-content data-v-51b5538d"><text class="header-title data-v-51b5538d">AI控制台</text><view class="header-actions data-v-51b5538d"><view data-event-opts="{{[['tap',[['refreshAiStatus',['$event']]]]]}}" class="action-btn refresh-btn data-v-51b5538d" bindtap="__e"><image class="action-icon-img data-v-51b5538d" src="https://u3w.com/chatfile/shuaxin.png" mode="aspectFit"></image></view><view data-event-opts="{{[['tap',[['showHistoryDrawer',['$event']]]]]}}" class="action-btn history-btn data-v-51b5538d" bindtap="__e"><image class="action-icon-img data-v-51b5538d" src="https://u3w.com/chatfile/lishi.png" mode="aspectFit"></image></view><view data-event-opts="{{[['tap',[['createNewChat',['$event']]]]]}}" class="action-btn new-chat-btn data-v-51b5538d" bindtap="__e"><image class="action-icon-img data-v-51b5538d" src="https://u3w.com/chatfile/chuangjian.png" mode="aspectFit"></image></view></view></view></view><scroll-view class="main-scroll data-v-51b5538d" scroll-y="{{true}}" scroll-into-view="{{scrollIntoView}}" enhanced="{{true}}" bounces="{{true}}" show-scrollbar="{{false}}" fast-deceleration="{{false}}"><view class="section-block data-v-51b5538d" id="ai-config"><view data-event-opts="{{[['tap',[['toggleSection',['aiConfig']]]]]}}" class="section-header data-v-51b5538d" bindtap="__e"><text class="section-title data-v-51b5538d">AI选择配置</text><text class="section-arrow data-v-51b5538d">{{''+(sectionExpanded.aiConfig?'▼':'▶')+''}}</text></view><block wx:if="{{sectionExpanded.aiConfig}}"><view class="section-content data-v-51b5538d"><view class="ai-grid data-v-51b5538d"><block wx:for="{{$root.l1}}" wx:for-item="ai" wx:for-index="index" wx:key="index"><view class="{{['ai-card','data-v-51b5538d',ai.m0?'ai-enabled':'',!ai.m1?'ai-disabled':'']}}"><view class="ai-header data-v-51b5538d"><view class="ai-info data-v-51b5538d"><view class="ai-name-container data-v-51b5538d"><text class="{{['ai-name','data-v-51b5538d',!ai.m2?'name-disabled':'']}}">{{ai.$orig.name}}</text><block wx:if="{{ai.m3}}"><text class="login-required data-v-51b5538d">需登录</text></block><block wx:if="{{ai.m4}}"><text class="loading-text data-v-51b5538d">检查中...</text></block></view><switch style="transform:scale(0.8);" checked="{{ai.m5}}" disabled="{{ai.m6}}" color="#409EFF" data-event-opts="{{[['change',[['toggleAI',['$0','$event'],[[['aiList','',index]]]]]]]}}" bindchange="__e" class="data-v-51b5538d"></switch></view></view><block wx:if="{{ai.g0>0}}"><view class="ai-capabilities data-v-51b5538d"><block wx:for="{{ai.l0}}" wx:for-item="capability" wx:for-index="__i0__" wx:key="value"><view data-event-opts="{{[['tap',[['toggleCapability',['$0','$1'],[[['aiList','',index]],[['aiList','',index],['capabilities','value',capability.$orig.value,'value']]]]]]]}}" class="{{['capability-tag','data-v-51b5538d',capability.g1?'capability-active':'',capability.m7?'capability-disabled':'']}}" bindtap="__e"><text class="capability-text data-v-51b5538d">{{capability.$orig.label}}</text></view></block></view></block></view></block></view></view></block></view><view class="section-block data-v-51b5538d" id="prompt-input"><view data-event-opts="{{[['tap',[['toggleSection',['promptInput']]]]]}}" class="section-header data-v-51b5538d" bindtap="__e"><text class="section-title data-v-51b5538d">提示词输入</text><text class="section-arrow data-v-51b5538d">{{''+(sectionExpanded.promptInput?'▼':'▶')+''}}</text></view><block wx:if="{{sectionExpanded.promptInput}}"><view class="section-content data-v-51b5538d"><textarea class="prompt-textarea data-v-51b5538d" placeholder="请输入提示词" maxlength="2000" show-confirm-bar="false" auto-height="{{true}}" data-event-opts="{{[['input',[['__set_model',['','promptInput','$event',[]]]]]]}}" value="{{promptInput}}" bindinput="__e"></textarea><view class="prompt-footer data-v-51b5538d"><text class="word-count data-v-51b5538d">{{$root.g2+"/2000"}}</text><button class="{{['send-btn','data-v-51b5538d',!canSend?'send-btn-disabled':'']}}" disabled="{{!canSend}}" data-event-opts="{{[['tap',[['sendPrompt',['$event']]]]]}}" bindtap="__e">发送</button></view></view></block></view><block wx:if="{{taskStarted}}"><view class="section-block data-v-51b5538d" id="task-status"><view data-event-opts="{{[['tap',[['toggleSection',['taskStatus']]]]]}}" class="section-header data-v-51b5538d" bindtap="__e"><text class="section-title data-v-51b5538d">任务执行状态</text><text class="section-arrow data-v-51b5538d">{{''+(sectionExpanded.taskStatus?'▼':'▶')+''}}</text></view><block wx:if="{{sectionExpanded.taskStatus}}"><view class="section-content data-v-51b5538d"><view class="task-flow data-v-51b5538d"><block wx:for="{{$root.l3}}" wx:for-item="ai" wx:for-index="index" wx:key="index"><view class="task-item data-v-51b5538d"><view data-event-opts="{{[['tap',[['toggleTaskExpansion',['$0'],[[['enabledAIs','',index]]]]]]]}}" class="task-header data-v-51b5538d" bindtap="__e"><view class="task-left data-v-51b5538d"><text class="task-arrow data-v-51b5538d">{{''+(ai.$orig.isExpanded?'▼':'▶')+''}}</text><image class="task-avatar data-v-51b5538d" src="{{ai.$orig.avatar}}" mode="aspectFill"></image><text class="task-name data-v-51b5538d">{{ai.$orig.name}}</text></view><view class="task-right data-v-51b5538d"><text class="status-text data-v-51b5538d">{{ai.m8}}</text><text class="{{['status-icon','data-v-51b5538d',ai.m9]}}">{{''+ai.m10+''}}</text></view></view><block wx:if="{{ai.g3}}"><view class="progress-logs data-v-51b5538d"><block wx:for="{{ai.l2}}" wx:for-item="log" wx:for-index="logIndex" wx:key="logIndex"><view class="progress-item data-v-51b5538d"><view class="{{['progress-dot','data-v-51b5538d',log.$orig.isCompleted?'dot-completed':'']}}"></view><view class="progress-content data-v-51b5538d"><text class="progress-time data-v-51b5538d">{{log.m11}}</text><text class="progress-text data-v-51b5538d">{{log.$orig.content}}</text></view></view></block></view></block></view></block></view></view></block></view></block><block wx:if="{{$root.g4>0}}"><view class="section-block data-v-51b5538d" id="results"><view class="section-header data-v-51b5538d"><text class="section-title data-v-51b5538d">执行结果</text><button class="score-btn data-v-51b5538d" size="mini" data-event-opts="{{[['tap',[['showScoreModal',['$event']]]]]}}" bindtap="__e">智能评分</button></view><view class="section-content data-v-51b5538d"><scroll-view class="result-tabs data-v-51b5538d" scroll-x="{{true}}"><view class="tab-container data-v-51b5538d"><block wx:for="{{results}}" wx:for-item="result" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['switchResultTab',[index]]]]]}}" class="{{['result-tab','data-v-51b5538d',activeResultIndex===index?'tab-active':'']}}" bindtap="__e"><text class="tab-text data-v-51b5538d">{{result.aiName}}</text></view></block></view></scroll-view><block wx:if="{{currentResult}}"><view class="result-content data-v-51b5538d"><view class="result-actions data-v-51b5538d"><block wx:if="{{currentResult.shareUrl}}"><button class="share-link-btn data-v-51b5538d" size="mini" data-event-opts="{{[['tap',[['openShareUrl',['$0'],['currentResult.shareUrl']]]]]}}" bindtap="__e">复制原链接</button></block><button class="action-btn-small data-v-51b5538d" size="mini" data-event-opts="{{[['tap',[['copyResult',['$0'],['currentResult.content']]]]]}}" bindtap="__e">复制(纯文本)</button><button class="collect-btn data-v-51b5538d" size="mini" data-event-opts="{{[['tap',[['collectToOffice',['$0'],['currentResult.content']]]]]}}" bindtap="__e">投递到公众号</button></view><view class="result-body data-v-51b5538d"><block wx:if="{{$root.m12}}"><view class="result-image-container data-v-51b5538d"><image class="result-image data-v-51b5538d" src="{{currentResult.shareImgUrl}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$0'],['currentResult.shareImgUrl']]]]]}}" bindtap="__e"></image></view></block><block wx:else><block wx:if="{{$root.m13}}"><view class="result-pdf-container data-v-51b5538d"><view class="pdf-placeholder data-v-51b5538d"><view class="pdf-icon data-v-51b5538d">📄</view><text class="pdf-text data-v-51b5538d">PDF文件</text><view class="pdf-actions data-v-51b5538d"><button class="pdf-btn download-btn data-v-51b5538d" size="mini" data-event-opts="{{[['tap',[['openPdfFile',['$0'],['currentResult.shareImgUrl']]]]]}}" bindtap="__e">打开文件</button><button class="pdf-btn copy-btn data-v-51b5538d" size="mini" data-event-opts="{{[['tap',[['copyPdfUrl',['$0'],['currentResult.shareImgUrl']]]]]}}" bindtap="__e">复制链接</button></view></view></view></block><block wx:else><view class="result-text data-v-51b5538d"><rich-text nodes="{{$root.m14}}" class="data-v-51b5538d"></rich-text></view></block></block></view></view></block></view></view></block></scroll-view><block wx:if="{{historyDrawerVisible}}"><view data-event-opts="{{[['tap',[['closeHistoryDrawer',['$event']]]]]}}" class="drawer-mask data-v-51b5538d" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="drawer-container data-v-51b5538d" catchtap="__e"><view class="drawer-content data-v-51b5538d"><view class="drawer-header data-v-51b5538d"><text class="drawer-title data-v-51b5538d">历史会话记录</text><text data-event-opts="{{[['tap',[['closeHistoryDrawer',['$event']]]]]}}" class="close-icon data-v-51b5538d" bindtap="__e">✕</text></view><scroll-view class="history-list data-v-51b5538d" scroll-y="{{true}}"><block wx:for="{{$root.l5}}" wx:for-item="group" wx:for-index="date" wx:key="date"><view class="history-group data-v-51b5538d"><text class="history-date data-v-51b5538d">{{date}}</text><block wx:for="{{group.l4}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['loadHistoryItem',['$0'],[[['groupedHistory','',date],['','',index]]]]]]]}}" class="history-item data-v-51b5538d" bindtap="__e"><text class="history-prompt data-v-51b5538d">{{item.$orig.userPrompt}}</text><text class="history-time data-v-51b5538d">{{item.m15}}</text></view></block></view></block></scroll-view></view></view></view></block><block wx:if="{{scoreModalVisible}}"><view data-event-opts="{{[['tap',[['closeScoreModal',['$event']]]]]}}" class="popup-mask data-v-51b5538d" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="score-modal data-v-51b5538d" catchtap="__e"><view class="score-header data-v-51b5538d"><text class="score-title data-v-51b5538d">智能评分</text><text data-event-opts="{{[['tap',[['closeScoreModal',['$event']]]]]}}" class="close-icon data-v-51b5538d" bindtap="__e">✕</text></view><view class="score-content data-v-51b5538d"><view class="score-prompt-section data-v-51b5538d"><text class="score-subtitle data-v-51b5538d">评分提示词：</text><textarea class="score-textarea data-v-51b5538d" placeholder="请输入评分提示词，例如：请从内容质量、逻辑性、创新性等方面进行评分" maxlength="1000" data-event-opts="{{[['input',[['__set_model',['','scorePrompt','$event',[]]]]]]}}" value="{{scorePrompt}}" bindinput="__e"></textarea></view><view class="score-selection data-v-51b5538d"><text class="score-subtitle data-v-51b5538d">选择要评分的内容：</text><checkbox-group data-event-opts="{{[['change',[['toggleResultSelection',['$event']]]]]}}" bindchange="__e" class="data-v-51b5538d"><view class="score-checkboxes data-v-51b5538d"><block wx:for="{{$root.l6}}" wx:for-item="result" wx:for-index="index" wx:key="index"><label class="checkbox-item data-v-51b5538d"><checkbox value="{{result.$orig.aiName}}" checked="{{result.g5}}" class="data-v-51b5538d"></checkbox><text class="checkbox-text data-v-51b5538d">{{result.$orig.aiName}}</text></label></block></view></checkbox-group></view><button class="score-submit-btn data-v-51b5538d" disabled="{{!canScore}}" data-event-opts="{{[['tap',[['handleScore',['$event']]]]]}}" bindtap="__e">开始评分</button></view></view></view></block></view>